import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import legacy from "@vitejs/plugin-legacy";
import UnoCSS from "unocss/vite";
import svgLoader from "vite-svg-loader";

// https://vitejs.dev/config/
export default defineConfig({
  base: process.env.NODE_ENV === "development" ? "./" : "/dwyztApp",
  plugins: [vue(), UnoCSS(), svgLoader(), legacy()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    host: "0.0.0.0",
    proxy: {
      "/action/topoEdit": {
        target: "http://**************:8899",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/action/, ""),
      },
      "/action": {
        target: "http://**************:8899",
        // target: "http://*************:8087/mock/189",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/action/, ""),
      },

      "/ftp": {
        target: "http://**************:6818",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ftp/, "/ftp"),
      },
    },
  },
});
