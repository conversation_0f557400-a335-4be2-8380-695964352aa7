<template>
  <NModal
    v-model:show="isVisible"
    draggable
    title="查询"
    class="important-repair"
    preset="card"
    style="width: 500px; background-color: #1a2f42; margin-top: 20vh"
  >
    <NForm ref="formValidate" :label-width="80" label-placement="left">
      <NFormItem label="时间范围" prop="timeRange">
        <NDatePicker
          format="yyyy/MM/dd"
          type="daterange"
          placeholder="选择时间"
          v-model:value="timeRange"
        />
      </NFormItem>
      <NFormItem label="匹配条件" prop="matchRule">
        <NSelect
          :options="nodeLinkOptions"
          @search="remoteMethod"
          @update:value="keyNameUpdate"
          filterable
          remote
          clearable
          placeholder="厂站或线路"
        >
        </NSelect>
      </NFormItem>
    </NForm>
    <template #footer>
      <div class="flex justify-end">
        <NButton text @click="hide" class="mr-5">取消</NButton>
        <NButton color="#2bb0db" @click="ok">确认</NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup>
import { ref } from "vue";
// import { Message } from "view-ui-plus";
import { useTopoStore } from "@/stores/";
import { NModal, NForm, NFormItem, NButton, NSelect, NDatePicker } from "naive-ui";
import dayjs from "dayjs";
import { useNodeLinkSearch } from "@/hooks/";

const { nodeLinkOptions, remoteMethod } = useNodeLinkSearch();

const topoStore = useTopoStore();

const isVisible = ref(false);
const timeRange = ref([Date.now(), Date.now()]);
const keyName = ref("");
const nodeLinkSeachValue = ref({});

function show() {
  isVisible.value = true;
}
function hide() {
  isVisible.value = false;
  keyName.value = "";
  timeRange.value = [Date.now(), Date.now()];
  nodeLinkOptions.value = [];
}

const keyNameUpdate = (v, option) => {
  nodeLinkSeachValue.value = option;
};

function getRecentSixMonthsDateRange() {
  const today = new Date();
  const start = new Date(today.getFullYear(), today.getMonth() - 6, 1);
  const end = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const startDate =
    start.getFullYear() +
    "-" +
    ("0" + (start.getMonth() + 1)).slice(-2) +
    "-" +
    ("0" + start.getDate()).slice(-2);
  const endDate =
    end.getFullYear() +
    "-" +
    ("0" + (end.getMonth() + 1)).slice(-2) +
    "-" +
    ("0" + end.getDate()).slice(-2);
  return {
    startDate,
    endDate,
  };
}

function ok() {
  let startDate = timeRange.value[0];
  let endDate = timeRange.value[1];
  if (!startDate || !endDate) {
    const date = getRecentSixMonthsDateRange();
    startDate = date.startDate;
    endDate = date.endDate;
  }
  topoStore.setTabVisible(true);

  const key = nodeLinkSeachValue.value.value;

  topoStore.searchParams = {
    startTime: dayjs(startDate).format("YYYY-MM-DD"),
    endTime: dayjs(endDate).format("YYYY-MM-DD"),
    key,
  };
  if (topoStore.dataType === "计划平衡") {
    topoStore.getBalanceInfoListMonthly(topoStore.searchParams);
  } else {
    topoStore.getOverHaulInfoList(topoStore.searchParams);
  }
  hide();
}

defineExpose({
  show,
  hide,
});
</script>
