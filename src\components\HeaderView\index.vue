<template>
  <div class="header-bg flex justify-between items-center pr-4 h-15">
    <img
      src="../../assets/images/commons/header_title.webp"
      alt=""
      srcset=""
      class="h-full object-fill"
    />

    <n-tabs type="line" class="w-50" v-model:value="commonStore.activeTab">
      <n-tab name="basic">
        <span class="tab-text text-xl text-[#e1ffdc]">基础监视</span>
      </n-tab>
      <n-tab name="map"> <span class="tab-text text-xl text-[#e1ffdc]">地图信息</span> </n-tab>
    </n-tabs>

    <div class="flex items-center">
      <div
        v-for="item in dataTypeList"
        :key="item"
        class="data-type-btn"
        :class="{ 'data-type-btn-active': topoStore.dataType === item }"
        @click="onDataTypeChange(item)"
      >
        {{ item }}
      </div>
    </div>
  </div>
  <ImportantRepair ref="importRepair"></ImportantRepair>
</template>

<script setup>
import { ref, watchEffect } from "vue";
import { useRouter, useRoute } from "vue-router";
import ImportantRepair from "@/components/ImportantRepair/index.vue";
import { useTopoStore, useCommonStore } from "@/stores/";
import { NTabs, NTab } from "naive-ui";

const dataTypeList = ["检修计划", "开工计划", "计划平衡"];
const importRepair = ref(null);
const isDev = ref(true);
const displayName = ref("");
const activeName = ref("2");

const router = useRouter();
const route = useRoute();
const topoStore = useTopoStore();
const commonStore = useCommonStore();

isDev.value = import.meta.env.MODE === "development";

displayName.value = sessionStorage.getItem("displayName");

watchEffect(() => {
  if (router.currentRoute.value.path === "/index" && route.query.type === "repair") {
    activeName.value = "2";
    topoStore.setMenuSelect("2");
  } else if (router.currentRoute.value.path === "/map") {
    activeName.value = "3";
    topoStore.setMenuSelect("3");
  }
});

function onDataTypeChange(val) {
  topoStore.dataType = val;
  topoStore.repairList = [];
  topoStore.searchParams = {};
  topoStore.setTabVisible(false);
}

const onTabChange = (val) => {
  if (val === "基础监视") {
    router.push("/index");
  } else if (val === "地图信息") {
    router.push("/map");
  }
};
</script>

<style lang="scss" scoped>
.header-bg {
  background: linear-gradient(to bottom, #2f5b89, #23456a, #1d3755, #13283e);
}

.title {
  font-size: 22px;
  font-weight: bold;
  background: #2196f3;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  //   text-shadow: 0 0px 2px rgba(0, 0, 0, 0.35);

  background-image: linear-gradient(to bottom, #fefefe, #8e8e8e);
}
.data-type-btn {
  background-image: url("../../assets/images/commons/switch_btn.webp");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 139px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  color: #fff;
  font-size: 18px;
  margin-left: -12px;
  opacity: 0.5;
  user-select: none;
  cursor: pointer;
}
.data-type-btn:hover {
  opacity: 0.85;
}
.data-type-btn-active {
  opacity: 1;
}

.tab-text {
  text-shadow:
    0 0 1px rgba(255, 255, 255, 0.8),
    0 0 1px rgba(255, 255, 255, 0.8),
    0 0 2px rgba(255, 255, 255, 0.8);
}
</style>
