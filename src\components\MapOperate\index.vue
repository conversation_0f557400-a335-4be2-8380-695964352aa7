<template>
  <div class="map-operate-layout flex h-14 justify-between items-center">
    <div class="flex items-center">
      <NButton text style="font-size: 26px" @click="resetTopo">
        <NIcon>
          <HomeIcon />
        </NIcon>
      </NButton>

      <NButton text style="font-size: 26px" class="mx-4" @click="fullScreen">
        <NIcon>
          <ZoomOutMapRoundIcon />
        </NIcon>
      </NButton>

      <!-- <NCheckbox
        v-if="topoStore.dataType === '检修计划'"
        v-model:checked="repairStatus"
        class="mr-5"
        @update:checked="changeRepairStatus"
        >检修态</NCheckbox
      > -->

      <NCheckbox
        v-if="isAreaVisible"
        v-model:checked="stopStatus"
        class="mr-5"
        @update:checked="changeStopStatus"
        >{{ title }}着色</NCheckbox
      >

      <NCheckbox
        v-if="['检修计划', '计划平衡'].includes(topoStore.dataType)"
        v-model:checked="repairTag"
        class="mr-5"
        @update:checked="setRepairTag"
        >{{ title }}标签
      </NCheckbox>

      <NCheckbox
        v-if="isAreaVisible"
        v-model:checked="divideFill"
        class="mr-5"
        @update:checked="changeDivideFill"
        >分区着色</NCheckbox
      >
    </div>
    <div class="flex items-center">
      <div class="flex items-center">
        <n-checkbox
          size="small"
          label="220kV"
          v-model:checked="commonStore.is220ViewVisible"
          @update:checked="switchTopo($event, '220kV')"
        />
        <n-checkbox
          size="medium"
          label="110kV"
          v-model:checked="commonStore.is110ViewVisible"
          @update:checked="switchTopo($event, '110kV')"
        />
        <n-checkbox
          size="medium"
          label="35kV"
          v-model:checked="commonStore.is35ViewVisible"
          @update:checked="switchTopo($event, '35kV')"
        />
        <NSelect
          :options="nodeLinkOptions"
          @search="remoteMethod"
          @update:value="keyNameUpdate"
          style="width: 150px"
          filterable
          remote
          clearable
          size="small"
          placeholder="厂站或线路"
        >
        </NSelect>
        <NButton
          color="#2bb0db"
          @click="searchEquip"
          class="search-btn ml-1"
          size="small"
          type="default"
          :disabled="loading"
          >查询</NButton
        >
      </div>
      <!-- <div class="flex mr-5 items-center" v-if="isAreaVisible"> -->
      <!-- <span style="margin-right: 10px">分区</span>
        <NSelect
          clearable
          :disabled="loading"
          :options="areaOptions"
          placeholder="选择分区"
          size="small"
          style="width: 100px"
          class="mr-3"
          @update:value="changeSub"
        >
        </NSelect> -->

      <NButton
        v-if="isAreaVisible"
        @click="onImportmentRepairClick"
        color="#2bb0db"
        class="search-btn ml-1"
        size="small"
        type="default"
        >{{ title }}查询</NButton
      >
      <!-- </div> -->
    </div>
  </div>
  <ImportantRepair ref="importantRepairRef"></ImportantRepair>
</template>

<script setup>
import { computed, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { useTopoStore, useCommonStore } from "@/stores/";
import HomeIcon from "@/assets/images/icons/Home.svg";
import ZoomOutMapRoundIcon from "@/assets/images/icons/ZoomOutMapRound.svg";
import {
  switchTopo,
  resetTopo,
  fullScreen,
  updateRepairColor,
  updateSvg,
  activeValue,
  activeStopColor,
  changeDivideFill,
  setRepairTag,
  zoom110,
} from "@/utils/draw/";
import ImportantRepair from "@/components/ImportantRepair/index.vue";
import { movePosition } from "@/utils/operateNodeLink.js";
import { locatePosition } from "@/utils/draw/modules/event.js";
import { NButton, NSelect, NCheckbox, NIcon } from "naive-ui";
import { useNodeLinkSearch } from "@/hooks/";
import { emitter } from "@/utils/event";

const { nodeLinkOptions, remoteMethod } = useNodeLinkSearch();
const topoStore = useTopoStore();
const commonStore = useCommonStore();
let colorTimer = null;
let timer = null;
let loading = false;

const importantRepairRef = ref(null);
const nodeLinkSeachValue = ref(null);
const repairStatus = ref(false);
const stopStatus = ref(false);
const repairTag = ref(false);
const divideFill = ref(false);
// (⚠️废弃)
const switchRepairStatus = async (status) => {
  if (status) {
    let colorData = await topoStore.getColorData();
    updateRepairColor(colorData);

    colorTimer = setInterval(async () => {
      let colorData = await topoStore.getColorData();
      updateRepairColor(colorData);
    }, 60 * 1000);

    // if(JSON.stringify(this.colorData) !== "{}"){
    //     this._updateRepairColor(this.colorData)
    // }
  } else {
    if (colorTimer != null) {
      clearInterval(colorTimer);
    }
    const realData = await topoStore.getRealData(topoStore.miniSvgData.map.mapId);
    updateSvg(realData);
  }
};

const areaOptions = computed(() => {
  return topoStore.subList.map((item) => {
    return {
      label: item.hotspotName,
      value: item.hotspotName,
    };
  });
});

const keyNameUpdate = (v, option) => {
  nodeLinkSeachValue.value = option;
};

const changeStopStatus = (v) => {
  // topoStore.stopStatus = v;
  activeStopColor(v);
};

// 切换检修状态(⚠️废弃)
const changeRepairStatus = (v) => {
  //TODO data为 接口调用数据
  const data = [];
  switchRepairStatus(v, data);
  activeValue(!v);

  divideFill.value = false;
  changeDivideFill(false);
};

const onImportmentRepairClick = () => {
  importantRepairRef.value.show();
};

// 查找线路和电厂
const searchEquip = () => {
  if (nodeLinkSeachValue.value) {
    commonStore.is110 = true;
    let pos = movePosition(nodeLinkSeachValue.value);
    console.log("🚀 ~ searchEquip ~ pos:", pos);
    locatePosition(pos, [nodeLinkSeachValue.value]);
  }
};

const changeSub = (v) => {
  if (!v) {
    commonStore.isChange = true; //可以自由切换220和500
    return;
  }
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
  loading = true;
  timer = setTimeout(() => {
    loading = false;
  }, 3000);
  //筛选分区并计算出分区位置
  let hotpotLinks = topoStore.miniSvgData.links
    .filter((l) => {
      let style = JSON.parse(l.linkStyles);
      return style.isBlock;
    })
    .map((i) => {
      return {
        linkPath: i.linkPath,
        name: i.metaData.name,
        id: i.linkId,
      };
    });
  let currenPot = hotpotLinks.filter((h) => {
    return h.name.indexOf(v) > -1;
  })[0];

  console.log(v, hotpotLinks);

  console.log(currenPot);
  //计算分区位置和放大比例
  let points = currenPot.linkPath
    .split("M")[1]
    .split("Z")[0]
    .split("L")
    .map((d) => d.trim().split(" "));
  console.log(points);
  countPos(points, currenPot.id);

  // this.topoComp.flyToSub(oriPos);
};

//计算位移和缩放比例
const countPos = (points, id) => {
  let minX = Math.min(
    ...points.map((i) => {
      return Number(i[0]);
    }),
  );
  let maxX = Math.max(
    ...points.map((i) => {
      return Number(i[0]);
    }),
  );
  let minY = Math.min(
    ...points.map((i) => {
      return Number(i[1]);
    }),
  );
  let maxY = Math.max(
    ...points.map((i) => {
      return Number(i[1]);
    }),
  );
  console.log(minX, maxX, minY, maxY);
  let cw = maxX - minX;
  let ch = maxY - minY;
  let sw = document.getElementById("mainView").offsetWidth;
  let sh = document.getElementById("mainView").offsetHeight;
  console.log(cw, ch, sw, sh);

  let k = Math.min(sw / cw, sh / ch); //缩放比
  let pos = {
    x: -minX * k + (sw / 2 - ((maxX - minX) / 2) * k),
    y: -minY * k + (sh / 2 - ((maxY - minY) / 2) * k),
    k: k,
  };
  // console.log(this.nodeKeyId, node, link);

  console.log("pos", pos);
  // this.topoComp.flyToSub(pos);
  commonStore.is110 = true;
  commonStore.isChange = false;
  d3.select("#svg_panel .mainSVG").select(".mydefs").remove();
  d3.select("#svg_panel2 .mainSVG")
    .transition()
    .duration(1000)
    .call(zoom110.transform, d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k));

  // setTimeout(() => {
  //     this.isChange = true;
  // }, 1000)

  // this.topoComp.blockFlash(id, true);
  d3.select("#svg_panel2 .mainSVG")
    .select("#block-container")
    .select(`#topoLink_${id}`)
    .classed("topo-flash", true);
  setTimeout(() => {
    d3.select("#svg_panel2 .mainSVG")
      .select("#block-container")
      .select(`#topoLink_${id}`)
      .classed("topo-flash", false);
  }, 5000);
};

const isAreaVisible = computed(() => {
  return ["检修计划", "计划平衡"].includes(topoStore.dataType);
});

const title = computed(() => {
  return topoStore.dataType === "检修计划" ? "检修" : "停电";
});

watch(
  () => topoStore.dataType,
  () => {
    repairStatus.value = false;
    repairTag.value = false;
    stopStatus.value = false;
    changeStopStatus(false);
    setRepairTag(false);
    changeRepairStatus(false);
  },
);

const initEvent = () => {
  emitter.on("update:changeColor", () => {
    console.log("-----着色-----");

    activeStopColor(stopStatus.value);
  });
};

const offEvent = () => {
  emitter.off("update:changeColor");
};
onMounted(() => {
  initEvent();
});

onBeforeUnmount(() => {
  offEvent();
});
</script>
