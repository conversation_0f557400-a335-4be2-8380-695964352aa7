<template>
  <NLayout has-sider sider-placement="right" class="h-full">
    <NLayoutContent v-show="commonStore.activeTab === 'basic'">
      <div class="map-layout flex">
        <div class="map-conatiner flex flex-1 flex-col p-3 pt-0 mr-3">
          <MapOperate></MapOperate>
          <MainView></MainView>
        </div>
        <!-- <RepairView></RepairView> -->
      </div>
    </NLayoutContent>
    <div v-show="commonStore.activeTab === 'map'" class="flex flex-1 overflow-hidden">
      <GisView type="map"></GisView>
    </div>
    <RepairView></RepairView>
  </NLayout>

  <ImportantRepair ref="importantRepair"></ImportantRepair>
</template>

<script setup>
import { onMounted } from "vue";
import { NLayout, NLayoutContent } from "naive-ui";
import { useCommonStore, useGisStore } from "@/stores/";

import RepairView from "@/components/RepairView/index.vue";
import ImportantRepair from "@/components/ImportantRepair/index.vue";
import MainView from "@/components/MainView/index.vue";
import MapOperate from "@/components/MapOperate/index.vue";
import GisView from "@/components/MainView/GisView/index.vue";

const commonStore = useCommonStore();
const gisStore = useGisStore();

const initData = async () => {
  console.log("当前 BASE_URL =", import.meta.env.BASE_URL);

  const url = import.meta.env.BASE_URL + "static/data/config.json";

  const res = await fetch(url);
  const config = await res.json();
  gisStore.mapUrl = config.mapUrl;
};

onMounted(async () => {
  initData();
});
</script>

<style lang="scss">
@use "../../assets/scss/moudle/page.scss";
</style>
