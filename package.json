{"name": "businessdeepen", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@popperjs/core": "^2.11.8", "@stomp/stompjs": "^7.1.1", "axios": "^1.10.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.3", "pinyin-engine": "^1.2.2", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "v-calendar": "^3.1.2", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@unocss/eslint-config": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@vitejs/plugin-legacy": "^7.0.1", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-oxlint": "^1.7.0", "eslint-plugin-vue": "^10.3.0", "globals": "^16.3.0", "naive-ui": "^2.42.0", "npm-run-all2": "^8.0.4", "oxlint": "^1.7.0", "prettier": "^3.6.2", "sass-embedded": "^1.89.2", "unocss": "^66.3.3", "vite": "^7.0.5", "vite-svg-loader": "^5.1.0"}}