.home-page {
  background-color: #16263d;
}
.map-layout {
  background-color: #16263d;
  height: 100%;
}
.map-conatiner {
  background-color: #1a2f42;
}
.map-operate-layout {
  background-color: #1a2f42;
  border-bottom: 2px solid #5a6d8e;
}
.topMenu {
  background: black;
  width: 100%;
  height: 50px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20px;
}
.top-menu-left {
  display: flex;
  align-items: center;
}
.top-menu-right {
  display: flex;
  align-items: center;
}
.exit {
  cursor: pointer;
}
:deep(.ivu-btn-ghost.ivu-btn-default[disabled]){
  color: white !important;
}
.icon {
  color: white;
  &::hover {
    opacity: 0.6;
  }
}
.bottomMenu {
  background: black;
  width: 100%;
  height: 50px;
  position: absolute;
  bottom: 0;
  border-top: 1px solid #ddd;
  display: flex;
  align-items: center;
  padding-left: 25px;
}

.middleCon {
  background: #1a2f42;
  width: 100%;
  height: calc(100% - 20px);
  display: flex;
  padding-top: 10px;
  overflow: hidden;
  .mainView {
    flex: 1;
    background: #1a2f42;
    height: 100%;
    overflow: hidden;
    position: relative;
    // padding: 10px;
    .myModal {
      background: white;
      left: calc(50% - 600px);
      top: 5%;
      transition: transform 0.5s ease-in-out;
      .myTitle {
        color: black;
        font-weight: bold;
        font-size: 28px;
        text-align: center;
        .closeModal {
          float: right;
          cursor: pointer;
        }
      }
    }
  }
  .rightDetail {
    width: 400px;
    // position: absolute;
    // right: 0;
    height: 100%;
    background: #b2b1b1;
    border-left: 10px solid black;
    transition: all 0.2s ease-in-out;
    .rightCollapse {
      position: absolute;
      right: 390px;
      color: white;
      background: red;
      z-index: 999;
      top: 50%;
      width: 10px;
      height: 24px;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
    }
    :deep(.ivu-tabs-bar) {
      background: white;
      // padding: 0 10px;
    }
    :deep(.ivu-tabs .ivu-tabs-tabpane) {
      padding: 0 10px;
    }
    :deep(.ivu-tabs-nav-prev) {
      display: none !important;
    }
    :deep(.ivu-tabs-nav-next) {
      display: none !important;
    }
    :deep(.ivu-tabs-nav-scrollable) {
      padding: 0 !important;
    }
    .actionBtn {
      color: blue;
    }
  }
}
.tipOpen {
  right: 390px !important;
  border-radius: 0 5px 5px 0;
}
.tipClose {
  right: 0px !important;
  border-radius: 5px 0 0 5px;
}
.contextmenu {
  position: absolute;
  background: #3e3e3e;
  color: #c5b495;
  list-style: none;
  border-radius: 5px;
  padding: 3px 5px;
  cursor: pointer;
  li {
    // margin-bottom: 5px;
    padding: 5px 10px;
    margin: 2px 0;
    background: #303030;
  }
  li:hover {
    // color: red;
    background: #8b8b8b;
  }
}
.popCon {
  width: 100%;
}

.search-btn {
  background-image:linear-gradient(to bottom right, #0bbce3, #1b94d3, #2d68c1);
  border: none;
  padding: 0 15px;
  border-radius: 2px;
  :hover {
    opacity: 0.75;
  }
}

#textWidth {
  position: absolute;
  display: inline-block;
  line-height: 1;
  visibility: hidden;
  white-space: nowrap;
  z-index: -900;
  white-space: pre;
}
