<template>
  <NTabs
    v-model:value="tabActiveName"
    type="card"
    tab-class="my-tab-layout"
    @update:value="getData"
    @close="onTabRemove"
  >
    <!-- <NTabPane tab="当月" name="1">
      <RepairList :data="topoStore.repairList" @on-row-click="onRowClick"></RepairList>
    </NTabPane> -->
    <NTabPane tab="次月" name="2">
      <RepairList :data="topoStore.balanceList" @on-row-click="onRowClick"></RepairList>
    </NTabPane>
    <NTabPane tab="第三月" name="3">
      <RepairList :data="topoStore.balanceList" @on-row-click="onRowClick"></RepairList>
    </NTabPane>
    <NTabPane v-if="topoStore.isTabVisible" tab="查询停电" closable name="4">
      <RepairList :data="topoStore.balanceList" @on-row-click="onRowClick"> </RepairList>
    </NTabPane>
  </NTabs>
  <div class="absolute top-0 right-0 p-2 flex items-center">
    <NSelect
      v-model:value="topoStore.planBalanceState"
      :options="StateOptions"
      size="small"
      class="w-25 mr-2"
      @update:value="getData"
    />
    <NButton size="small" class="search-btn" @click="exportData">
      <!-- <NIcon>
        <ExitIcon />
      </NIcon> -->
      导出
    </NButton>
  </div>
  <!-- <RepairList :data="topoStore.repairList"></RepairList> -->
</template>

<script setup>
import dayjs from "dayjs";
import { NTabs, NTabPane, NSelect, NButton, NIcon } from "naive-ui";
import { useTopoStore } from "@/stores/";
import RepairList from "../RepairList.vue";
import { onMounted, ref, watch } from "vue";
import { onItemCLick } from "@/utils/operateNodeLink.js";
import { exportGantt } from "@/utils/gantt/";
import { StateOptions } from "@/utils/constant/";

const topoStore = useTopoStore();

const tabActiveName = ref("2");
const startTime = ref("");
const endTime = ref("");

const getTableData = async () => {
  await topoStore.getBalanceInfoListMonthly(
    tabActiveName.value === "4"
      ? topoStore.searchParams
      : {
          startTime: startTime.value,
          endTime: endTime.value,
        },
  );
};
/**
 *
 * @param type
 *  1 当月
 *  2 次月
 *  3 第三月
 */
const getRepairTimeRange = (type = "1") => {
  const currentDate = dayjs();
  let startTime = "";
  let endTime = "";

  if (type === "1") {
    startTime = currentDate.startOf("month").format("YYYY-MM-DD");
    endTime = currentDate.endOf("month").format("YYYY-MM-DD");
  } else if (type === "2") {
    startTime = currentDate.add(1, "month").startOf("month").format("YYYY-MM-DD");
    endTime = currentDate.add(1, "month").endOf("month").format("YYYY-MM-DD");
  } else if (type === "3") {
    startTime = currentDate.add(2, "month").startOf("month").format("YYYY-MM-DD");
    endTime = currentDate.add(2, "month").endOf("month").format("YYYY-MM-DD");
  }

  return {
    startTime,
    endTime,
  };
};

const getData = async () => {
  topoStore.repairList = [];
  const date = getRepairTimeRange(tabActiveName.value);

  startTime.value = date.startTime;
  endTime.value = date.endTime;

  await getTableData();
};

onMounted(() => {
  tabActiveName.value = "2";
  getData();
});

watch(
  () => topoStore.isTabVisible,
  (val) => {
    tabActiveName.value = val ? "4" : "2";
  },
);
const onRowClick = (row) => {
  onItemCLick(row);
};

const exportData = () => {
  exportGantt(topoStore.repairList, tabActiveName.value);
};

const onTabRemove = () => {
  topoStore.setTabVisible(false);
  tabActiveName.value = "2";
  getData();
};
</script>

<style>
.n-tabs .n-tabs-nav.n-tabs-nav--card-type .n-tabs-tab.n-tabs-tab--active {
  background-color: #2f65a5 !important;
}
.n-tabs .n-tabs-nav.n-tabs-nav--card-type .n-tabs-tab {
  color: #b1b4b8 !important;
}
</style>
