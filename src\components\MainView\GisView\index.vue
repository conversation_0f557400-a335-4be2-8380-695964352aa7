<template>
  <iframe
    ref="iframeRef"
    frameborder="0"
    width="100%"
    height="100%"
    :src="gisStore.mapUrl"
    overflow="hidden"
  ></iframe>
</template>

<script setup>
import { onMounted, useTemplateRef, nextTick, watch, onBeforeUnmount } from "vue";
import { useGisStore, useCommonStore } from "@/stores/";
import { emitter } from "@/utils/event";

const props = defineProps({
  type: {
    type: String,
    default: "basic",
  },
});

const gisStore = useGisStore();
const iframeRef = useTemplateRef("iframeRef");

function canAccessIframe(iframe) {
  try {
    // 尝试读取同源才可见的属性
    void iframe.contentWindow.location.href;
    return true;
  } catch (e) {
    return false;
  }
}

const sendMessage = async () => {
  await nextTick();
  iframeRef.value.onload = () => {
    if (canAccessIframe(iframeRef.value)) {
      iframeRef.value.contentWindow.flyToDevices(gisStore.mapData);
    } else {
      // 跨域，走 postMessage
      iframeRef.value.contentWindow.postMessage(
        {
          action: "flyToDevices",
          data: gisStore.mapData,
        },
        "*",
      );
    }
  };
};

const initEvent = () => {
  // type对应commonStore中的activeTab，防止多次触发
  emitter.on("onMapData", (type) => {
    console.log("🚀 ~ emitter.on ~ data:", gisStore.mapData);
    // sendMessage(data);
  });
};

const offEvent = () => {
  emitter.off("onMapData");
};

onMounted(async () => {
  initEvent();
});

onBeforeUnmount(() => {
  console.log("onBeforeUnmount");
  offEvent();
});

defineExpose({
  sendMessage,
});
</script>
