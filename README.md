# 一张图综合应用

## 项目简介
一张图综合应用是电力系统中的可视化展示与管理平台，提供电网实时状态监控、检修计划管理、工作计划安排等功能。该系统通过整合多维数据，实现对电网运行全景的直观展示和高效管理，助力电网运维决策。

## 技术栈
- 前端框架：Vue 3
- 状态管理：Pinia
- 路由管理：Vue Router
- 网络请求：Axios
- 样式处理：SCSS
- 构建工具：Vite

## Git所属分支  
http://*************:8088/powerDispatching/businessDeepen/-/tree/tz
- tz    泰州分支

## 目录结构
```
src                                 
├─ assets                           
│  ├─ API                           
│  │  ├─ API.js                     
│  │  └─ serviceAddress.txt         
│  ├─ data                          
│  │  └─ OpticalCable.js            
│  ├─ images                        
│  └─ scss                                     
├─ components                       
│  ├─ HeaderView        //头部            
│  │  └─ index.vue                  
│  ├─ ImportantRepair   //重要检修            
│  │  └─ index.vue                  
│  ├─ MainView          //首页 全景图和110切换            
│  │  └─ index.vue                  
│  ├─ MapOperate        //电网图顶部操作：检修着色 检修标签等            
│  │  └─ index.vue                  
│  ├─ RepairView        //检修页面，默认显示检修计划            
│  │  ├─ PlanBalance    //计划平衡            
│  │  │  └─ index.vue               
│  │  ├─ RepairPlan     //检修计划            
│  │  │  └─ index.vue               
│  │  ├─ WorkPlan       //开工计划            
│  │  │  ├─ data.json   // 模拟数据            
│  │  │  ├─ EventCalendar.vue  //开工计划日历，显示工作状态     
│  │  │  ├─ index.vue               
│  │  │  └─ WorkDetail.vue     //开工计划底部列表      
│  │  ├─ index.vue                  
│  │  └─ RepairList.vue //检修计划和计划平衡共用的列表组件                                          
├─ hooks                            
│  ├─ index.js                               
│  ├─ useNodeLinkSearch.js     //远程搜索hook                    
├─ http                             
│  ├─ axios.js         //网络请求封装             
│  ├─ config.js        //网络配置项             
│  └─ index.js                      
├─ router                                                
│  └─ index.js         //路由设置             
├─ stores              //pinia数据             
│  ├─ modules                       
│  │  ├─ common.js     //通用数据             
│  │  ├─ plan.js       //开工计划数据             
│  │  └─ topo.js       //电网图和检修数据             
│  └─ index.js                      
├─ utils                            
│  ├─ constant                      
│  │  ├─ modules                    
│  │  │  └─ resource.js//图片资源统一导出             
│  │  └─ index.js                   
│  ├─ draw                          
│  │  ├─ modules                    
│  │  │  ├─ assistant.js//初始化位置 全屏函数            
│  │  │  ├─ constant.js //电网图颜色更新常量            
│  │  │  ├─ data.js     //获取更新数据函数，定时获取数据            
│  │  │  ├─ draw.js     //根据数据更新电网图颜色 两张图切换            
│  │  │  ├─ event.js    //绑定电网图缩放事件，重置大小，移动，元素双击事件            
│  │  │  └─ repair.js   //切换检修状态，更新电网图            
│  │  └─ index.js                   
│  ├─ event             //emitter/event-bus           
│  │  └─ index.js                   
│  ├─ gantt                         
│  │  └─ index.js       //生成甘特图（Excel），导出            
│  ├─ operateNodeLink.js//电网图操作工具类：移动位置，转换stationId，获取浮窗弹出位置            
│  └─ searchChinese.js  //通过拼音查询中文            
├─ views                            
│  ├─ 403                           
│  │  └─ index.vue                                 
│  ├─ main                          
│  │  └─ index.vue      //首页布局                          
│  ├─ Home.vue                      
│  └─ Login.vue                     
├─ App.vue                          
└─ main.js                          
```

## 核心功能模块详解

### 1. 基础监视
- **功能介绍**: 提供全景图和110kV电网图切换展示，通过可视化方式直观展现电网运行状态
- **技术实现**: 自研电网图渲染引擎，支持元素缩放、平移、点击交互
- **关键文件**: 
  - `components/MainView/index.vue` - 视图组件
  - `utils/draw/modules` - 绘制核心逻辑

获取电网图方式：   
`src\utils\draw\modules\draw.js` 
  ```
  // 开发环境
  import dwyzt220 from "../../assets/images/dwyzt220.svg";
  import dwyzt500 from "../../assets/images/dwyzt500.svg";
  
   // 生产环境
   d3.xml(`/dwyztApp/ftp/data/dwyzt500.svg`).then(function (xml) {
            let svgContent = xml.documentElement;
            d3.select("#svg_panel").node().appendChild(svgContent);
          });
          d3.xml(`/dwyztApp/ftp/data/dwyzt220.svg`).then(function (xml) {
            let svgContent = xml.documentElement;
            d3.select("#svg_panel2").node().appendChild(svgContent);
          });
  ```

### 2. 检修管理系统 (RepairView)
- **功能介绍**: 包含检修计划、计划平衡、开工计划三大模块，实现检修全流程管理
- **检修计划**: 展示和管理检修任务安排
- **计划平衡**: 对各时段检修计划进行负荷平衡分析
- **开工计划**: 通过日历方式展示检修任务开工状态，支持详细信息查看

### 3. 状态监控与着色 (MapOperate)
- **功能介绍**: 支持电网图元素状态实时更新与着色，直观展示检修、故障等状态
- **技术实现**: 定时获取后端数据，根据业务规则动态更新图元样式
- **关键文件**: 
  - `components/MapOperate/index.vue` - 操作界面
  - `utils/draw/modules/repair.js` - 状态更新逻辑

## 数据流管理

### Pinia 状态管理
- **common.js**: 存储系统通用配置、用户信息等
- **plan.js**: 管理开工计划相关数据
- **topo.js**: 电网图数据和检修数据统一管理

### 数据获取流程
1. 通过 `utils/draw/modules/data.js` 定时从后端拉取最新电网状态数据
2. 数据通过 Pinia 存储并分发至各组件
3. 组件订阅数据变化，及时更新视图

## 开发指南

### 环境配置
1. 安装依赖：`yarn install`
2. 启动开发服务器：`yarn dev`
3. 构建生产版本：`yarn build`

### 新功能开发流程
1. 确定功能所属模块，在相应目录创建组件
2. 在 Pinia store 中添加必要的状态管理
3. 实现业务逻辑与视图
4. 通过路由系统集成至应用

## 常见问题与解决方案

### 多分支代码管理
- V3分支用于电网功能开发，包含完整电网展示功能
- tz分支针对泰州地区定制，包含特定业务逻辑
- 核心功能更新需同时合并至各活跃分支