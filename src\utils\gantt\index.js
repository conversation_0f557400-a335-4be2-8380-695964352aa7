import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import dayjs from "dayjs";
import minMax from "dayjs/plugin/minMax"; // ES 2015
dayjs.extend(minMax);

let tasks = [];
// "东风变"
const TableTitles = [
  { key: "index", title: "序号", width: 8 },
  { key: "name", title: "名称", width: 25 },
  { key: "area", title: "停电范围", width: 40 },
  { key: "content", title: "工作内容", width: 50 },
  { key: "startDate", title: "开始", width: 12 },
  { key: "endDate", title: "结束", width: 12 },
  { key: "duration", title: "持续时间（天）", width: 15 },
  { key: "percentage", title: "完成", width: 15 },
];

function calculateDuration(startDate, endDate) {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  return end.diff(start, "day") + 1; // 包含起止当天
}

// percentage是开始时间到当前时间前一天天数与总天数的比例，如果结束时间在当前之前完成，则为100%，开始时间在当前时间之后，则为0%
function calculatePercentage(startDate, endDate) {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const totalDays = end.diff(start, "day") + 1; // 包含起止当天
  const currentDays = dayjs().diff(start, "day");
  if (currentDays < 0) {
    return 0;
  } else if (currentDays >= totalDays) {
    return "100%";
  }
  return ((currentDays / totalDays) * 100).toFixed(1) + "%";
}

function groupDataByCategoryAndProjectDes(data) {
  // 按一级分类（category）分组
  const categoryMap = data.reduce((acc, item) => {
    const category = item.category;
    if (!acc[category]) acc[category] = [];
    acc[category].push(item);
    return acc;
  }, {});

  const result = [];

  // 遍历每个一级分类
  Object.entries(categoryMap).forEach(([category, categoryItems]) => {
    // --- 计算一级节点的时间范围和持续时间 ---
    const categoryStartDates = categoryItems.map((item) => item.startDate).sort();
    const categoryEndDates = categoryItems
      .map((item) => item.endDate)
      .sort((a, b) => a.localeCompare(b));
    const categoryStart = categoryStartDates[0];
    const categoryEnd = categoryEndDates[categoryEndDates.length - 1];
    const categoryDuration = calculateDuration(categoryStart, categoryEnd);

    // 创建一级节点
    const categoryNode = {
      type: "一级",
      name: category,
      startDate: categoryStart,
      endDate: categoryEnd,
      duration: categoryDuration,
      percentage: calculatePercentage(categoryStart, categoryEnd),
    };
    result.push(categoryNode);

    // --- 按二级分类（projectDes）分组 ---
    const projectDesMap = categoryItems.reduce((acc, item) => {
      const projectDes = item.projectDes;
      if (!acc[projectDes]) acc[projectDes] = [];
      acc[projectDes].push(item);
      return acc;
    }, {});

    // 遍历每个二级分类
    Object.entries(projectDesMap).forEach(([projectDes, projectItems]) => {
      // --- 计算二级节点的时间范围和持续时间 ---
      const projectStartDates = projectItems.map((item) => item.startDate).sort();
      const projectEndDates = projectItems.map((item) => item.endDate).sort();
      const projectStart = projectStartDates[0];
      const projectEnd = projectEndDates[projectEndDates.length - 1];
      const projectDuration = calculateDuration(projectStart, projectEnd);

      // 计算二级节点占比（相对于一级节点）
      // const projectPercentage = ((projectDuration / categoryDuration) * 100).toFixed(1) + "%";

      // 创建二级节点
      const projectNode = {
        type: "二级",
        name: projectDes,
        startDate: projectStart,
        endDate: projectEnd,
        duration: projectDuration,
        percentage: calculatePercentage(projectStart, projectEnd),

        // percentage: projectPercentage,
      };
      result.push(projectNode);

      // --- 处理三级节点（原始数据）---
      projectItems.forEach((item) => {
        // 计算三级节点持续时间
        const itemDuration = calculateDuration(item.startDate, item.endDate);
        // 计算三级节点占比（相对于一级节点）
        // const itemPercentage = ((itemDuration / categoryDuration) * 100).toFixed(2) + "%";

        // 创建三级节点
        const itemNode = {
          type: "三级",
          ...item,
          duration: itemDuration,
          // percentage: itemPercentage,
          percentage: calculatePercentage(item.startDate, item.endDate),
        };
        result.push(itemNode);
      });
    });
  });

  return result;
}

/**
 * 通过task列表获取日期的跨度，返回一个数组
 * 假如时间跨度为 2024-01-21 到 2024-03-05
 * 返回 [{date: '2024-01', days: ['2024-01-21', '2024-01-22', '2024-01-23', ... '2024-01-31']}, {date: '2024-02', days: ['2024-02-01', '2024-02-02', '2024-02-03', ... '2024-02-28']}, {date: '2024-03', days: ['2024-03-01', '2024-03-02', '2024-03-03', ... '2024-03-05']}]
 */
function getDateRangeSpan(tasks) {
  // 1. 获取所有任务的日期并找到最小和最大日期
  const allDates = tasks.flatMap((task) => [dayjs(task.startDate), dayjs(task.endDate)]);
  const minDate = dayjs.min(allDates);
  const maxDate = dayjs.max(allDates);

  // 2. 初始化结果数组
  const result = [];

  // 3. 遍历从最小日期到最大日期的每一天
  let currentDate = minDate;
  while (currentDate.isBefore(maxDate) || currentDate.isSame(maxDate, "day")) {
    const yearMonth = currentDate.format("YYYY-MM"); // 获取当前日期的年月（如 '2024-01'）
    const day = currentDate.format("YYYY-MM-DD"); // 获取当前日期的完整日期（如 '2024-01-21'）

    // 查找是否已经存在该年月的分组
    let monthGroup = result.find((group) => group.date === yearMonth);
    if (!monthGroup) {
      // 如果不存在，创建一个新的分组
      monthGroup = { date: yearMonth, days: [] };
      result.push(monthGroup);
    }

    // 将当前日期添加到对应分组的 days 数组中
    monthGroup.days.push(day);

    // 移动到下一天
    currentDate = currentDate.add(1, "day");
  }

  return result;
}

const borderStyle = {
  top: { style: "thin" },
  left: { style: "thin" },
  bottom: { style: "thin" },
  right: { style: "thin" },
};

/**
 *  生成表头
 * @param {*} worksheet
 * @param {*} dates
 */
const generateTitle = (worksheet, dates) => {
  const titleFill = { type: "pattern", pattern: "solid", fgColor: { argb: "d9d9d9" } };

  // 动态添加表头
  TableTitles.forEach((item, index) => {
    const colIndex = index + 1;
    const cell = worksheet.getCell(1, colIndex);
    cell.value = item.title;
    cell.alignment = { vertical: "middle", horizontal: "left" };
    cell.fill = titleFill;
    cell.border = borderStyle;
    cell.font = { bold: true };

    worksheet.getCell(2, colIndex).border = borderStyle;
    worksheet.getColumn(colIndex).width = item.width;
    worksheet.mergeCells(1, colIndex, 2, colIndex); // 合并单元格
  });

  let startColNo = TableTitles.length + 1; // 从表头后面一列开始

  dates.forEach((month) => {
    const startColName = worksheet.getColumn(startColNo).letter;
    const endColName = worksheet.getColumn(startColNo + month.days.length - 1).letter;
    worksheet.mergeCells(`${startColName}1:${endColName}1`);
    const monthCell = worksheet.getCell(1, startColNo);
    monthCell.value = `${dayjs(month.date).format("YYYY年MM月")}`;
    monthCell.alignment = { vertical: "middle", horizontal: "center" };
    monthCell.fill = titleFill;
    monthCell.border = borderStyle;
    monthCell.font = { bold: true };

    // 添加当前月的每一天
    month.days.forEach((day, index) => {
      const col = worksheet.getColumn(startColNo + index); // 从当前列开始
      col.width = 6; // 设置列宽

      // 第二行：日期（YYYY-MM-DD）
      const dayCell = worksheet.getCell(2, startColNo + index);
      dayCell.value = dayjs(day).format("DD");
      dayCell.alignment = { vertical: "middle", horizontal: "left" };
      dayCell.fill = titleFill;
      dayCell.border = borderStyle;
      dayCell.font = { bold: true };
    });

    // 更新起始列号
    startColNo += month.days.length;
  });
};

function getFillColor(type) {
  switch (type) {
    case "一级":
      return "62a39f";
    case "二级":
      return "b3e3d5";
    case "三级":
      return "a9d8b6";
    default:
      return "ffffff"; // 默认白色
  }
}

const fillGantt = (worksheet, dates) => {
  tasks.forEach((task, rowIndex) => {
    // 添加任务信息
    const row = worksheet.getRow(3 + rowIndex);
    row.height = task.type === "一级" ? 30 : task.type === "二级" ? 25 : undefined;

    TableTitles.forEach((item, index) => {
      const colIndex = index + 1;
      const cell = row.getCell(colIndex);
      cell.value = task[item.key];
      cell.alignment = { vertical: "middle", horizontal: "left", wrapText: true };
      cell.border = borderStyle;

      if (item.key === "name") {
        cell.font = { bold: task.type !== "三级", size: task.type === "一级" ? 14 : undefined };
        cell.alignment = { vertical: "middle", horizontal: "left" };
      }
    });

    // 填充甘特图进度
    let colIndex = TableTitles.length + 1; // 从表头后面一列开始
    dates.forEach((month) => {
      month.days.forEach((day) => {
        const cell = row.getCell(colIndex);
        const dTime = dayjs(day).valueOf();

        cell.border = borderStyle;
        if (dTime >= task.start.valueOf() && dTime <= task.end.valueOf()) {
          cell.value = ""; // 不需要显示具体值
          const fillColor = getFillColor(task.type);
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: fillColor },
          };
        }
        colIndex++;
      });
    });
  });
};

const formatData = (data) => {
  if (!data) {
    return [];
  }
  return data.map((item, index) => {
    return {
      ...item,
      index: index + 1,
      name: item.name || item.stationName,
      start: dayjs(item.startDate),
      end: dayjs(item.endDate),
      // 使用dayjs计算持续时间
      // duration: dayjs(item.endDate).diff(dayjs(item.startDate), "day") + 1,
    };
  });
};

const balanceType = {
  1: "当月",
  2: "次月",
  3: "第三月",
};
export const exportGantt = (data, type) => {
  tasks = formatData(groupDataByCategoryAndProjectDes(data));
  // console.log("🚀 ~ exportGantt ~ tasks:", tasks);
  // 创建 Excel 工作簿
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("甘特图");

  const dates = getDateRangeSpan(tasks);

  generateTitle(worksheet, dates);
  fillGantt(worksheet, dates);
  // 冻结前两行
  worksheet.views = [{ state: "frozen", ySplit: 2 }];
  // 保存文件
  workbook.xlsx.writeBuffer().then(function (buffer) {
    saveAs(
      new Blob([buffer], { type: "application/octet-stream" }),
      `计划平衡-${balanceType[type]}-甘特图.xlsx`,
    );
  });
};
