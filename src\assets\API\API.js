/**
 * name: 公共接口
 * author: jiangguoxing
 * createTime: 2017-07-26
 */
(() => {
    //获取当前API.js的文件路径
    let currentJsPath = $("script").last().attr("src");
    currentJsPath = currentJsPath.substring(0, currentJsPath.lastIndexOf("/"));
    //let currentJsPath = (window.WisUtil && window.WisUtil.scriptPath) ? window.WisUtil.scriptPath("API") : "API";

    //后台服务路径, websocket服务地址, 数据同步服务地址, APNS ws 服务地址
    let [BASE_URL, CONTROL_CENTER, DATA_CENTER, VIDEO_CENTER, DEBUG_MODEL] = ["", "", "", "", ""];
    //本地文件地址
    const CONFIG_PATH = './sysconfig';
    //统一组件心跳定时器
    let sendKeepAliveIntervaler = null;
    let heartBeatTime = 5000;

    let resetConsoleLog = (debugModel) => {
        if (debugModel === "0") {
            console.log = () => { };
        }
    }

    let initAPI = () => {
        return new Promise((resolve, reject) => {
            d3.text(`${currentJsPath}/serviceAddress.txt?t=${Date.now()}`, res => {
                let parameters = res && res.replace(/[ ]/g, "").split(/[\n]+/g) || [];
                window.serviceAddressConfig = {};
                parameters.forEach(parameter => {
                    if (parameter && parameter.indexOf("#") != 0) {
                        let options = parameter.split("=");
                        window.serviceAddressConfig[options[0]] = options[1];
                    }
                });
                [BASE_URL, CONTROL_CENTER, DATA_CENTER, VIDEO_CENTER, DEBUG_MODEL] =
                    [serviceAddressConfig.BASE_URL, serviceAddressConfig.CONTROL_CENTER, serviceAddressConfig.DATA_CENTER, serviceAddressConfig.VIDEO_CENTER, serviceAddressConfig.DEBUG_MODEL];

                //重写console.log
                resetConsoleLog(DEBUG_MODEL);

                //全局实例注册
                window.asyncHttp = new AsyncHttp();
                window.wiscomWebSocket = new WiscomWebSocket();
                window.centralManager = new CentralManager(heartBeatTime);
                window.wiscomWebSocket.getDataSourceWS();
                //统一组件发送心跳至控制中心
                sendKeepAliveIntervaler = setInterval(() => {
                    window.wiscomWebSocket.webSocketList.forEach(ws => {
                        //ws处于连接状态，数据订阅不需要发送心跳
                        if (ws.readyState === 1 || ws.state === "OPEN")
                            ws.send(ws.keepAlive);
                    });
                }, heartBeatTime);
                resolve();
            });
        });
    }

    /**
    * 基本http服务
    */
    class AsyncHttp {
        constructor(time = 30000, user = {}) {
            this.time = time;
            //当前用户
            this.user = user;
            //身份认证
            this.token = null;
        };

        /**
         * 登录接口
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: {username, password}
         */
        requestLogin(params = {}) {
            let self = this;
            d3.request(BASE_URL + "/" + params.action)
                .mimeType("application/json")
                .response(xhr => { return JSON.parse(xhr.responseText); })
                .send("post", params.data, (error, resp) => {
                    self.user = params.data;
                    params.callback(resp);
                });
        };

        /**
         * 身份认证接口
         * @param {*} action: 接口路径
         * @param {json} data: {username, password}
         */
        _authentication(params = {}) {
            let self = this;
            d3.request(BASE_URL + "/" + params.action)
                .mimeType("application/json")
                .response(xhr => { return JSON.parse(xhr.responseText); })
                .send("post", params.data, error, resp => {
                    self.user = params.data;
                    self.token = resp.data && resp.data.token;
                });
        };

        /**
         * 设置请求超时时间，默认30秒
         */
        setRequestTimeout(time = 30000) {
            this.time = time;
            d3.request.timeout(this.time);
        };

        /**
         * 设置request身份验证
         * @param {*} params
         */
        setRequestAuth() {
            d3.request.user(this.user.username);
            d3.request.password(this.user.password);
        };

        /**
         * 公共JSON数据接口
         * @param {json} params
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: 请求参数，可以为空
         * @param {*} method: 方法类型
         */
        requestJSON(params = {}) {
            if (params.data) {
                d3.request(BASE_URL + "/" + params.action)
                    .mimeType("application/json;charset=utf-8")
                    .header("X-Requested-With", "XMLHttpRequest")
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .response(xhr => { return JSON.parse(xhr.responseText); })
                    .send(!params.method ? "GET" : params.method, params.data, params.callback);
            } else {
                d3.json(BASE_URL + "/" + params.action, params.callback);
            }
        };

        /**
         * 公共JSON数据接口
         * @param {json} params
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: 请求参数，可以为空
         * @param {*} method: 方法类型
         */
        sRequestJSON(params = {}) {
            if (params.data) {
                d3.request(params.action)
                    .mimeType("application/json;charset=utf-8")
                    .header("X-Requested-With", "XMLHttpRequest")
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .response(xhr => { return JSON.parse(xhr.responseText); })
                    .send(!params.method ? "GET" : params.method, params.data, params.callback);
            } else {
                d3.json(params.action, params.callback);
            }
        };

        /**
         * 公共TEXT数据接口
         * @param {json} params
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: 请求参数，可以为空
         * @param {*} method: 方法类型
         */
        requestText(params = {}) {
            if (params.data) {
                d3.request(BASE_URL + "/" + params.action)
                    .mimeType("text/plain")
                    .response(xhr => { return xhr.responseText; })
                    .send(!params.method ? "get" : params.method, params.data, params.callback);
            } else {
                d3.json(BASE_URL + "/" + params.action, params.callback);
            }
        };

        /**
         * 公共CSV数据接口
         * @param {json} params
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: 请求参数，可以为空
         * @param {*} method: 方法类型
         */
        requestCSV(params = {}) {
            if (params.data) {
                d3.request(BASE_URL + "/" + params.action)
                    .mimeType("text/csv")
                    .response(xhr => {
                        return d3.csvParse(xhr.responseText, d => {
                            return {
                                year: new Date(+d.Year, 0, 1),
                                make: d.Make,
                                model: d.Model,
                                length: +d.Length
                            };
                        });
                    })
                    .send(!params.method ? "get" : params.method, params.data, params.callback);
            } else {
                d3.json(BASE_URL + "/" + params.action, params.callback);
            }
        };

        /**
         * 公共HTML数据接口
         * @param {json} params
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: 请求参数，可以为空
         * @param {*} method: 方法类型
         */
        requestHTML(params = {}) {
            if (params.data) {
                d3.request(BASE_URL + "/" + params.action)
                    .mimeType("text/html")
                    .response(xhr => { return document.createRange().createContextualFragment(xhr.responseText); })
                    .send(!params.method ? "get" : params.method, params.data, params.callback);
            } else {
                d3.json(BASE_URL + "/" + params.action, params.callback);
            }
        };

        /**
         * 公共XML数据接口
         * @param {json} params
         * @param {*} action: 接口路径
         * @param {function} callback: 执行函数
         * @param {json} data: 请求参数，可以为空
         * @param {*} method: 方法类型
         */
        requestXML(params = {}) {
            if (params.data) {
                d3.request(BASE_URL + "/" + params.action)
                    .mimeType("application/xml")
                    .response(xhr => { return xhr.responseXML; })
                    .send(!params.method ? "get" : params.method, params.data, params.callback);
            } else {
                d3.json(BASE_URL + "/" + params.action, params.callback);
            }
        };

        /**
         * 加密
         * @param {*} info
         * @param {*} key
         */
        encrypt(info, key) {

        };

        /**
         * 解密
         * @param {*} info
         * @param {*} key
         */
        decrypt(info, key) {

        };

        /**
         * 加载解析多个文件
         * @param  {Array}    filenames
         * @param  {Function} callback
         * @param  {obj}   fileIcons  
         * @param  {number}   i
         * @return {void}
         */
        loadFiles(filenames, callback, fileIcons = [], i = 0) {
            let self = this;
            if (filenames[i].split(".")[1] === "svg" || filenames[i].split(".")[1] === "html") {
                d3.html(CONFIG_PATH + "/" + filenames[i], (error, datum) => {
                    fileIcons[i] = datum;
                    if (i < filenames.length - 1) {
                        self.loadFiles(filenames, callback, fileIcons, i + 1);
                    } else {
                        callback(fileIcons);
                    }
                });
            } else if (filenames[i].split(".")[1] === "json") {
                d3.json(CONFIG_PATH + "/" + filenames[i], (error, datum) => {
                    fileIcons[i] = datum;
                    if (i < filenames.length - 1) {
                        self.loadFiles(filenames, callback, fileIcons, i + 1);
                    } else {
                        callback(fileIcons);
                    }
                });
            }
        };

        /**
         * 加载解析并计算多个csv文件
         * @param  {Array}    filenames
         * @param  {Function} callback 
         * @param  {number}   sum   
         * @param  {number}   i
         * @return {void}
         */
        loadCSVFilesAndCalculateSum(filenames, callback, sum = 0, i = 0) {
            let self = this;
            d3.text(CONFIG_PATH + "/" + filenames[i], (error, text) => {
                sum += d3.csv.parseRows(text).reduce((prev, curr) => {
                    return prev + d3.sum(curr, d => { return +d; })
                }, 0);

                if (i < filenames.length - 1) {
                    self.loadFilesAndCalculateSum(filenames, callback, sum, i + 1);
                } else {
                    callback(sum);
                }
            });
        };
    };

    /**
    * websocket服务
    */
    class WiscomWebSocket {
        constructor() {
            //websocket服务列表
            this.webSocketList = [];
            //重连时间
            this.reconnectTime = 2000;
            //topicClientMap
            this.topicClientMap = new Map();

            //心跳检测
            this.heartCheck = {
                timeout: 60000, //心跳响应时间60s
                timeoutObj: null,
                serverTimeoutObj: null,
                reset: () => {
                    clearTimeout(this.timeoutObj);
                    clearTimeout(this.serverTimeoutObj);
                    return this;
                },
                start: ws => {
                    let self = this;
                    this.timeoutObj = setTimeout(() => {
                        //发送心跳，后端返回心跳消息; onmessage拿到返回的心跳就说明连接正常
                        ws.send("HeartBeat");
                        self.serverTimeoutObj = setTimeout(() => {
                            //如果超过一定时间还没重置，说明后端主动断开了
                            ws.close();
                        }, self.timeout);
                    }, this.timeout);
                }
            };
        };

        /**
         * 组件数据同步getDataSourceWS
         * @param {*} appName 
         * @param {*} serviceHost 
         * @param {*} port 
         * @param {*} url 
         */
        getDataSourceWS(ws = {}) {
            if (typeof (WebSocket) != "function") {
                console.log("当前浏览器不支持HTML5 WebSocket");
                return;
            }
            let isConnectError = false;
            let afreshSubscribe = document.createEvent('HTMLEvents');
            afreshSubscribe.initEvent("afreshSubscribe", true, true);

            let webSocket = new ReconnectingWebSocket(DATA_CENTER);
            //保存WebSocket应用
            webSocket.appName = "dataSource";
            //this.webSocketList.push(webSocket);
            let stompClient = Stomp.over(webSocket);
            stompClient.connect({}, success => {
                console.log("jw_debug web connection successfully", success);
                if (!isConnectError) return;
                document.dispatchEvent(afreshSubscribe);
                isConnectError = false;
            }, error => {
                console.log("jw_debug web connection failed", error);
                isConnectError = true;
            });
            window.dataSourceStompClient = stompClient;
        };

        /**
         * 数据订阅
         * @param {*} stompClient 
         * @param {*} componentID 
         * @param {*} callback 
         */
        subscribeData(componentID, callback) {
            let this_ = this;
            let stompClient = window.dataSourceStompClient;
            if (!stompClient.connected) {
                setTimeout(() => {
                    this_.subscribeData(componentID, callback);
                }, 2000);
            } else {
                console.log(`jw_debug subscribe: ${window.currentSceneId}, ${componentID}`);
                let topicClient = stompClient.subscribe(`/topic/TableData/${window.currentSceneId}/${componentID}`, callback);
                this_.topicClientMap.set(window.currentSceneId + "_" + componentID, topicClient);
            }
        };

        /**
         * 取消映射订阅
         */
        unsubscribeData(componentID) {
            let stompClient = window.dataSourceStompClient;
            console.log(`jw_debug unsubscribe: ${window.currentSceneId}, ${componentID}`);
            stompClient.connected && stompClient.send(`/topic/DisTableData/${window.currentSceneId}/${componentID}`);
            /*let topicKey = window.currentSceneId + "_" + componentID;
            if (this.topicClientMap.has(topicKey)) {
                //this.topicClientMap.get(topicKey).unsubscribe();
                this.topicClientMap.delete(topicKey);
            }*/
        }

        /**
         * 取消数据订阅
         * @param {*} componentID 
         */
        unsubscribeDataByClient(componentID) {
            let topicKey = window.currentSceneId + "_" + componentID;
            if (this.topicClientMap.has(topicKey)) {
                this.topicClientMap.get(topicKey).unsubscribe();
                this.topicClientMap.delete(topicKey);
            }
        }

        /**
         * 清空stompClientMap
         */
        emptyStompClient() {
            let stompClient = window.dataSourceStompClient;
            this.topicClientMap.forEach((item, key, map) => {
                if (stompClient.connected) {
                    try {
                        item && item.unsubscribe();
                    } catch (error) {
                        console.error(error);
                    }
                }
            });
            this.topicClientMap.clear();
        };

        /**
         * 获取websocket服务
         * @param {*} appName: 应用名称
         * @param {*} port: 服务端口
         * @param {*} url: websocket服务地址
         * @param {*} token: 身份认证(可以为空)
         * @return {WebSocket} webSocket
         */
        getWebSocket(appName = "ws", port = 6800, url = "", token, ws = {}) {
            if (typeof (WebSocket) != "function") {
                console.log("当前浏览器不支持HTML5 WebSocket");
                return;
            }
            //判断ws管理列表内是否已经存在初始化的ws
            let wsIndex = this.webSocketList.indexOf(ws);

            //支持身份认证，断点重连
            let webSocket = new ReconnectingWebSocket(`ws://${CONTROL_CENTER}:${port}${url ? `/${url}` : ""}${token ? `?token=${token}` : ""}`);
            //应用名称
            webSocket.appName = appName;
            //避免重复连接
            webSocket.lockReconnect = false;
            //事件队列
            webSocket.eventList = ws.eventList || [];
            //如果ws列表中已经存在ws
            if (wsIndex != -1) {
                this.webSocketList[wsIndex] = webSocket;
            } else {
                this.webSocketList.push(webSocket);
            }
            return webSocket;
        };

        /**
         * 获取APNS websocket服务
         * @param {*} name: ws名称
         */
        getAPNSWebsocket(name = "APNS") {
            if (typeof (WebSocket) != "function") {
                console.log("当前浏览器不支持HTML5 WebSocket");
                return;
            }

            //支持身份认证，断点重连
            let apns_webSocket = new ReconnectingWebSocket(`ws://${VIDEO_CENTER}`);
            //应用名称
            apns_webSocket.appName = name;
            apns_webSocket.keepAlive = "702:K_A";
            this.webSocketList.push(apns_webSocket);
            //apns_webSocket.heartBeatIntervaler = null;
            apns_webSocket.onopen = () => {
                apns_webSocket.state = "OPEN";
                //发送心跳，保持应用keepalive
                /*apns_webSocket.heartBeatIntervaler = setInterval(() => {
                    apns_webSocket.send("702:K_A");
                }, 5000);*/
            }
            apns_webSocket.onerror = e => {
                console.log("jw_debug apns_webSocket on error", e, e.data);
                /*clearInterval(apns_webSocket.heartBeatIntervaler);
                apns_webSocket.heartBeatIntervaler = null;*/
            };
            apns_webSocket.onclose = e => {
                console.log("jw_debug apns_webSocket on close", e, e.code);
                /*clearInterval(apns_webSocket.heartBeatIntervaler);
                apns_webSocket.heartBeatIntervaler = null;*/
            };
            return apns_webSocket;
        };

        /**
         * 重连websocket服务
         * @param {*} appName: 应用名称
         * @param {*} port: 服务端口
         * @param {*} url: websocket服务地址
         * @param {*} token: 身份认证(可以为空)
         * @param {WebSocket} ws
         */
        reconnect(appName = "ws", port = 6800, url = "", token, ws = {}) {
            if (ws.lockReconnect) return;
            ws.lockReconnect = true;

            //连接失败继续重连，设置延迟避免请求过多
            setTimeout(() => {
                this.getWebSocket(appName, port, url, token, ws);
                ws.lockReconnect = false;
            }, this.reconnectTime);
        };

        /**
         * 关闭websocket服务
         * @param {WebSocket} ws
         */
        disconnect(ws = {}) {
            ws.close();
            ws = null;
        };

        /**
         * 关闭所有websocket服务
         */
        disconnectAll() {
            this.webSocketList.forEach(ws => {
                this.disconnect(ws);
            });
            this.webSocketList = [];
        };

        /**
         * 获取所有webSocket应用
         * @return {Array} webSocketList
         */
        getAllWebSocket() {
            return this.webSocketList;
        };

        /**
         * 获取特定的WS
         * @param {*} appName 
         */
        resetWebSocketList(appName) {
            let saveWebSocketList = [];
            for (let index in this.webSocketList) {
                for (let i in appName) {
                    if (this.webSocketList[index].appName === appName[i]) {
                        saveWebSocketList.push(this.webSocketList[index]);
                    }
                }
            }
            this.webSocketList = saveWebSocketList;
        };
    };

    /**
     * 中心管理
     */
    class CentralManager {
        constructor(heartBeatTime = 5000) {
            //发送心跳间隔时间
            this.heartBeatTime = heartBeatTime;
            //通信码
            this.communicationCode = {
                "registerBrowser": 1,
                "registerComponent": 101,
                "sendMessage": 103,
                "receiveMessage": 104,
                "registerControl": 201,
                "syncConfig": 203,
                "updateScene": 204,
                "event": 205,
                "draw": 206,
                "command": 301
            };
        };

        /**
        * 创建ws回调
        * @param {*} appName: 应用名称
        * @param {*} executeFunc: 执行函数
        * @param {*} port: 端口
        * @param {*} url: websocket服务地址
        * @param {*} token: 身份认证
        * @param {WebScoket} ws
        * @param {*} register
        */
        _initEventHandle(appName = "ws", executeFunc = () => { }, port = 6800, url = "", token, ws = {}, register) {
            let heartBeatTime = this.heartBeatTime;
            let communicationCode = this.communicationCode;
            ws.onopen = e => {
                ws.state = "OPEN";
                //发送心跳，保持应用keepalive
                /*ws.heartBeatIntervaler = setInterval(() => {
                    ws.send(ws.keepAlive);
                }, heartBeatTime);*/

                //组件注册
                let registerMsg = `pane=${paneId},component=${ws.appName}`;
                console.log("注册=====" + registerMsg);
                ws.send(communicationCode[register ? register : "registerComponent"] + ":" + registerMsg);

                //判断是否存在需要发送的消息
                if (ws.eventList.length > 0) {
                    ws.eventList.forEach(event => {
                        ws.send(event);
                    });
                    ws.eventList = [];
                }
            };
            ws.onmessage = e => {
                if (e && e.data) {
                    var codeIndex = e.data.indexOf(':');
                    if (codeIndex < 1) {
                        console.log(`incorrected message format:${e.data}`);
                        return;
                    }
                    var code = parseInt(e.data.substring(0, codeIndex));
                    var message = e.data.substring(codeIndex + 1);

                    //接收返回消息执行相关操作
                    executeFunc(message, code);
                }
            };
            /*ws.onerror = e => {
                ws.state = "ERROR";
                //销毁心跳定时器
                clearInterval(ws.heartBeatIntervaler);
                ws.heartBeatIntervaler = null;
                //wiscomWebSocket.reconnect(appName, port, url, token, ws);
            };
            ws.onclose = e => {
                ws.state = "CLOSED";
                //销毁心跳定时器
                clearInterval(ws.heartBeatIntervaler);
                ws.heartBeatIntervaler = null;
                wiscomWebSocket.disconnect(ws);
            };*/
        };

        /**
         * 发送消息
         * @param {WebSocket} ws
         * @param {*} message
         */
        sendMessage(ws = {}, message = "default", code) {
            //判断当前webSocket是否处于连接状态
            if (ws.readyState === WebSocket.OPEN || ws.state === "OPEN") {
                ws.send(this.communicationCode[code ? code : "sendMessage"] + ":" + message);
            } else {
                console.log("websocket 未创建成功!");
                ws.eventList.push(this.communicationCode[code ? code : "sendMessage"] + ":" + message);
            }
        };

        /**
         * 组件注册
         * @param {WebSocket} ws
         * @param {*} message
         */
        registerComponent(ws = {}, message = "default", register) {
            //判断当前webSocket是否处于连接状态
            if (ws.readyState === WebSocket.OPEN || ws.state === "OPEN") {
                ws.send(this.communicationCode[register ? register : "registerComponent"] + ":" + message);
            } else {
                console.log("websocket 未创建成功!");
                ws.eventList.push(this.communicationCode[register ? register : "registerComponent"] + ":" + message);
            }
        };

        /**
         * 实时监听端口
         * @param {*} port: 6800, 6801, 6802
         */
        listenToPortEvents(port) {
            console.log("*****" + port);
        };

        /**
         * 大屏客户端注册服务
         * @param {*} appName: 应用名称
         * @param {*} executeFunc: 执行函数
         * @param {*} port: 端口
         * @param {*} url: websocket服务地址
         * @param {*} token: 身份认证
         * @return {WebScoket} connectWS
         */
        centralConnect(appName = "connectWS", executeFunc = () => { }, port = 6800, url = "", token) {
            let connectWS = wiscomWebSocket.getWebSocket(appName, port, url, token);
            connectWS.keepAlive = "2:K_A";

            this._initEventHandle(appName, executeFunc, port, url, token, connectWS, "registerBrowser");

            return connectWS;
        };

        /**
          * 组件事件同步服务
          * @param {*} appName: 应用名称
          * @param {*} executeFunc: 执行函数
          * @param {*} port: 端口
          * @param {*} url: websocket服务地址
          * @param {*} token: 身份认证
          * @return {WebScoket} synchWS
          */
        eventSynchronization(appName = "synchWS", executeFunc = () => { }, port = 6801, url = "", token) {
            let synchWS = wiscomWebSocket.getWebSocket(appName, port, url, token);
            synchWS.keepAlive = "102:K_A";

            this._initEventHandle(appName, executeFunc, port, url, token, synchWS);

            return synchWS;
        };

        /**
          * 控制客户端注册服务
          * @param {*} appName: 应用名称
          * @param {*} executeFunc: 执行函数
          * @param {*} port: 端口
          * @param {*} url: websocket服务地址
          * @param {*} token: 身份认证
          * @return {WebScoket} controlWS
          */
        controlCenter(appName = "controlWS", executeFunc = () => { }, port = 6802, url = "", token) {
            let controlWS = wiscomWebSocket.getWebSocket(appName, port, url, token);
            controlWS.keepAlive = "202:K_A";

            this._initEventHandle(appName, executeFunc, port, url, token, controlWS, "registerControl");

            return controlWS;
        };
    };

    //初始化API
    window.initAPIPromise = initAPI();
})();