import { createRouter, createWebHistory } from "vue-router";
import Cookies from "js-cookie";

import Login from "@/views/Login.vue";
import Home from "@/views/Home.vue";

const index = () => import("@/views/main/index.vue");

const routes = [
  {
    path: "/",
    name: "首页",
    component: Home,
    redirect: "/index",
    children: [
      {
        path: "/index",
        name: "index",
        component: index,
        meta: { title: "常州地调", keepAlive: true },
      },
    ],
  },
  {
    path: "/403",
    name: "无权限访问",
    meta: {
      title: "无权限访问",
      isLogin: false,
    },
    component: () => import("@/views/403/index.vue"),
  },
  {
    path: "/login",
    name: "Login",
    meta: {
      title: "登录",
    },
    component: Login,
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PREFIX),
  routes,
});

const initToken = (route) => {
  const { token } = route.query;
  if (token) {
    Cookies.set("token", token, { expires: 0.5 });
    // sessionStorage.setItem("token", token);
  }
};

router.beforeEach((to, from, next) => {
  initToken(to);
  document.title = to.meta["title"];
  // const token = sessionStorage.getItem("token");
  // const { token } = to.query;
  const token = Cookies.get("token");
  if (token) {
    if (to.path === "/index") {
      next();
    } else {
      next("/index");
    }
  } else {
    if (!token && to.path !== "/login") {
      next({ path: "/login" });
    } else {
      next();
    }
  }
});

export default router;
