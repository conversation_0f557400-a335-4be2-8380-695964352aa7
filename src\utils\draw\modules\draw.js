import * as d3 from "d3";
import { useCommonStore, useTopoStore } from "@/stores/";
import { resetSvgPosition, maintainSvgPosition } from "./event";
import { getRepairData } from "../../operateNodeLink";
import { getFillColor, getColor } from "./constant";
import { getElementId } from "@/utils/operateNodeLink.js";

// 沿用省调命名 实际为 500KV -> 220KV , 220KV -> 110KV
import dwyzt500Raw from "@/assets/images/maps/dwyzt500.svg?raw";
import dwyzt220Raw from "@/assets/images/maps/dwyzt220.svg?raw";

let linkCon = null;
let nodeCon = null;
let arrowCon = null;
let blockCon = null;

// export let defs = null;

// 更新文字节点
function _updateText(id, data) {
  d3.select(`#node_${id.replace("#", "_")}`).text(data.value);
}
// 更新变电站
function _updateTransStation(id, nodeMeta, data) {
  let con = d3.select(`#node_${id.replace("#", "_")}`);

  [...con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
    d3.select(p)
      .selectAll(".topo_xfmr_inset")
      .style("fill", getFillColor(data.xfmr[i]?.status, nodeMeta.volt));
  });

  // 更新低压母线数据;
  if (nodeMeta.lowBusNo !== "") {
    [...con.select(".lowbus").selectAll("g").nodes()].forEach((p, i) => {
      d3.select(p)
        .selectAll(".TransformerSubstation-inset")
        .style("fill", getFillColor(data.lowBus[i]?.status, nodeMeta.volt, true));
    });
  }
  // 更新高压母线数据;
  if (nodeMeta.highBusNo !== "") {
    [...con.select(".highbus").selectAll("g").nodes()].forEach((p, i) => {
      d3.select(p)
        .selectAll(".TransformerSubstation-inset")
        .style("fill", getFillColor(data.highBus[i]?.status, nodeMeta.volt));
    });
  }
  // 更新开关数据;
  if (nodeMeta.switchNo !== "") {
    let switchArray = [...con.select(".switch").selectAll("g").nodes()];

    switchArray.forEach((p, i) => {
      // 5: 停电 分位
      // 0: 正常 合位
      const status = data.switch[i] && data.switch[i].status;
      d3.select(p)
        .selectAll(".TransformerSwitch")
        .style("stroke-width", status == "0" ? 2 : 10)
        .style("stroke", status == "0" ? "black" : "#FF0000")
        .style("fill", status == "0" ? "#daa520" : null);
    });
  }
}
// 更新电厂
function _updatePowerStation(id, nodeMeta, data) {
  let con = d3.select(`#node_${id.replace("#", "_")}`);

  if (data === undefined) return;
  let barData = [];

  nodeMeta.no.split(",").forEach((d) => {
    barData.push({
      mvarate: 100,
      value: 0,
      name: nodeMeta[`keyDesc${d}`],
      no: d,
    });
  });

  barData = data.machineList.map((i, index) => {
    return {
      mvarate: i.mvarate,
      value: i.value,
      name: barData[index] && barData[index].name,
      no: barData[index] && barData[index].no,
    };
  });
  let drawRect = {
    x: 15,
    y: 17,
    width: 176,
    height: 176,
  };

  if (con.select("g.container") === undefined) return;
  requestAnimationFrame(() => {
    // con.select('g.container').selectAll('*').remove();

    // 更新比例尺
    let xRange = [];
    barData.forEach((d) => {
      xRange.push(
        d3
          .scaleLinear()
          .domain([0, Number(d.mvarate)])
          .range([drawRect.x, drawRect.width]),
      );
    });
    let yRange = d3
      .scaleBand()
      .domain(barData.map((d, i) => i))
      .range([drawRect.y, drawRect.y + drawRect.height]);

    // 更新bar
    let barG = con.select("g.container").select(".powerplant_bar");

    barG
      .selectAll(".rect")
      .data(barData)
      .attr("y", (d, i) => yRange.bandwidth() * i)
      .attr("height", yRange.bandwidth())
      .attr("width", (d, i) => xRange[i](d.value));

    if (nodeMeta.volt == "_500KV") {
      barG
        .selectAll(".endRect")
        .data(barData)
        .attr("x", (d, i) => xRange[i](d.value) - 2.5)
        .attr("y", (d, i) => yRange.bandwidth() * i)
        .attr("height", yRange.bandwidth());
    }

    // 更新机组名称
    let pattern = /(#\d+|(?<!\d)\d+号)/;
    let nameG = con.select("g.container").select(".powerplant_name");
    nameG
      .selectAll(".name")
      .data(barData)
      .style("font-size", `${50 - barData.length * 5}px`)
      .attr("y", (d, i) => yRange.bandwidth() * (i + 0.5))
      .attr("width", (d, i) => xRange[i](d.value))
      .text((d) => {
        if (!d.name) return;
        let n = d.name.match(pattern);
        if (!n) return "0";
        if (n[0].indexOf("号") != "-1") {
          return `#${n[0].slice(0, -1)}`;
        } else {
          return n[0];
        }
      });

    // 更新数值
    let valueG = con.select("g.container").select(".powerplant_value");
    valueG
      .selectAll(".value")
      .data(barData)
      .style("font-size", `${50 - barData.length * 5}px`)
      .attr("y", (d, i) => yRange.bandwidth() * (i + 0.5))
      .text((d) => `${Math.round(d.value)}`);
  });
}
// 更新换流站
function _updateExchStation(id, nodeMeta, data) {
  let con = d3.select(`#node_${id.replace("#", "_")}`);

  //更新高压母线数据
  if (nodeMeta.highBusNo !== "") {
    [...con.select(".highbus").selectAll("g").nodes()].forEach((p, i) => {
      d3.select(p)
        .selectAll(".TransformerSubstation-inset")
        .style("fill", getFillColor(data.highBus[i]?.status, nodeMeta.volt));
    });
  }
}

// 更新分区板  area_board_BoNAHGtxY0
function _updateAreaBoard(id, data) {
  let nameNode = nodeCon
    .select(`#area_board_${id.replace("#", "_")}`)
    .select(".area-board-name")
    .node();
  // console.log(nameNode)
  // console.log(d3.select(nameNode).text())
  nodeCon
    .select(`#area_board_${id.replace("#", "_")}`)
    .select(".area-board-value")
    .text(data.value);
  // console.log(name)
  nodeCon
    .select(`#area_board_${id.replace("#", "_")}`)
    .select("use")
    .attr("xlink:href", `#areaBoard${d3.select(nameNode).text().length}-${data.status}`);
  // return;
}

// 更新线路
function _updateAcLine(link, data) {
  let linkLen = 100;
  if (!linkCon.select(`#topoLink_${link.linkId.replace("#", "_")}`).empty()) {
    linkLen = linkCon
      .select(`#topoLink_${link.linkId.replace("#", "_")}`)
      .node()
      .getTotalLength();
  }
  // console.log(linkLen)
  // let arrPos =   linkCon.select(`#topoLink_${nodeId.replace('#', '_')}`).node()?.getPointAtLength(linkLen*0.5)

  let linkNode = linkCon.select(`#topoLink_${link.linkId.replace("#", "_")}`);

  let arrowGroup = arrowCon.select(`#topoArrow_${link.linkId.replace("#", "_")}`);
  //   if (arrowGroup.empty()) {
  //     arrowGroup = arrowCon.append("g").attr("id", `topoArrow_${link.linkId.replace("#", "_")}`);
  //   } else {
  arrowGroup.selectAll("polygon").remove();
  //   }

  // 根据线路状态绘制箭头 status :5 :无箭头, 其他：多箭头;  flash:1: 多箭头,
  let num = data.flash === "1" ? Math.ceil(linkLen / 20) : Math.ceil(linkLen / 40);

  if (data.status === "6") {
    num = 1;
  }
  if (link.metaData.volt == "_110KV" || link.metaData.volt == "_35KV") {
    num = 1;
  }
  if (data.status === "5") {
    num = 0;
  }
  for (let i = 0; i < num; i++) {
    _drawTopoArrow(link, linkNode, data, num, i);
  }
  linkCon
    .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    .classed("topo-isDivide", data.isDivide === "1")
    //   .classed("topo-flash", data.flash === "1")
    .attr("stroke-dasharray", data.status == "6" ? "2 2" : "none");
  if (data.status != "6") {
    linkCon
      .select(`#topoLink_${link.linkId.replace("#", "_")}`)
      .style("stroke", getFillColor(data.status, link.metaData.volt));
  }

  if (data.isDivide === "1") {
    data.nodes &&
      data.nodes.forEach((id) => {
        nodeCon.select(`#node_${id.replace("#", "_")}`).classed("topo-isDivide", true);
      });
  }
}

function _drawTopoArrow(link, linkDom, data, num, inx) {
  const commonStore = useCommonStore();

  if (link.compClass && link.compClass !== "") {
    // let id = link.id;
    let arrowGroup = arrowCon.select(`#topoArrow_${link.linkId.replace("#", "_")}`);

    let w =
      link.metaData.volt == "_110KV" || link.metaData.volt == "_35KV"
        ? 1
        : link.metaData.volt == "_220KV"
          ? 1.5
          : 2;
    let h =
      link.metaData.volt == "_110KV" || link.metaData.volt == "_35KV"
        ? 1
        : link.metaData.volt == "_220KV"
          ? 1.5
          : 2;
    let arrowArr = [
      [15, 0],
      [5, -4],
      [5, -2],
      [7, 0],
      // [0, 2],
      [5, 2],
      [5, 4],
    ];

    arrowArr.forEach((d) => {
      d[0] = d[0] * w * commonStore.svgScale;
      d[1] = d[1] * h * commonStore.svgScale;
      d = d.join(",");
    });
    // let pathLength = this.linkDom.node()?.getTotalLength();
    // if (!linkDom.empty()) {
    //     let pathLength = linkDom.node().getTotalLength();
    // }
    // console.log(pathLength/75)
    arrowGroup
      .append("polygon")
      // .attr('id', `topoArrow_${this.id.replace('#', '_')}`)
      .attr("points", arrowArr.join(" "))
      .style(
        "display",
        `${data.direction == "0" ? "none" : "block"} `,
        // this.style.volt == "500" || this.style.volt == "1000"
        //   ? "block"
        //   : this.data.status === "6"
        //   ? "block"
        //   : "none"
      )
      .attr("class", link.sublayerList ? link.sublayerList.map((d) => d.sublayerId).join(" ") : "")
      // .style('fill', 'url(#arrow-fill-white)')
      // .style('transform', 'translateZ(0)')
      // .style('will-change','transform')
      .style("transform", `rotate(${data.direction === "1" ? 0 : 180}deg) scale(1.2)`)
      .style("fill", getColor(link.metaData.volt))
      // .style("filter", "url(#arrow_shadow)")
      .style("offset-path", `path("${scalePath(link.linkPath, 0.1)}")`)

      .style("offset-distance", `${(100 / (num + 1)) * (inx + 1)}%`);
    //   .style(
    //     "animation",
    //     `arrowMove ${(pathLength / 20) * 1}s linear ${
    //       this.inx * (((pathLength / 20) * 1000) / this.num)
    //     }ms infinite`
    //   )
    // .style(
    //     "animation-direction",
    //     data.status === "6" ? "normal" : data.direction === "1" ? "normal" : "reverse"
    // );

    if (data.status === "6" || link.metaData.volt == "_110KV") {
      //   console.log(arrPos?.x)
      arrowGroup
        .selectAll("polygon")

        // .style('animation','null' )
        .style("offset-distance", "50%");
    }
  }
}

function scalePath(pathData, scaleFactor) {
  // 使用正则表达式匹配路径中的数字，包括小数
  const coordPattern = /[-+]?\d*\.?\d+/g;

  // 找到所有坐标值
  let coords = pathData.match(coordPattern);

  // 如果没有找到坐标值，直接返回原路径
  if (!coords) return pathData;

  // 缩小每个坐标值
  const scaledCoords = coords.map((coord) => parseFloat(coord) * scaleFactor);

  // 重建路径数据字符串，注意保留原有的命令字符（M, L等）
  let scaledPathData = pathData.replace(coordPattern, () => scaledCoords.shift().toFixed(10)); // toFixed用于控制小数位数

  return scaledPathData;
}

/**
 *
 * @param {*} data
 */
export const updateSvg = (data) => {
  const topoStore = useTopoStore();
  topoStore.miniSvgData.nodes.forEach((node) => {
    if (node.metaData) {
      let nodeMeta = node.metaData;
      if (!data || !data[node.nodeId]) {
        return;
      }

      switch (nodeMeta.type) {
        case "DText":
          // if(data[node.nodeId]){
          _updateText(node.nodeId, data[node.nodeId]);
          // }
          break;
        case "TransStation":
          _updateTransStation(node.nodeId, node.metaData, data[node.nodeId]);
          break;
        case "PowerStation":
          // 非火电厂不更新数据
          if (
            nodeMeta.num === 0 ||
            (nodeMeta.powerType !== "THERMAL" && nodeMeta.powerType !== "PUMP")
          )
            break;
          _updatePowerStation(node.nodeId, node.metaData, data[node.nodeId]);
          break;
        case "ExchStation":
          _updateExchStation(node.nodeId, node.metaData, data[node.nodeId]);
          break;
        case "AreaBoard": //!this.showSublayer.includes("KS5TXwwEDE") ||
          //   if (!this.showSublayer.includes("SDAfqbRLOiv")) break;
          _updateAreaBoard(node.nodeId, data[node.nodeId]);
          break;
        default:
          break;
      }
    }
  });
  topoStore.miniSvgData.links.forEach((link) => {
    if (link.metaData) {
      let nodeMeta = link.metaData;
      if (!data || !data[link.linkId]) {
        return;
      }
      switch (nodeMeta.type) {
        case "AcLine":
          _updateAcLine(link, data[link.linkId]);
          break;
        default:
          break;
      }
    }
  });
};

// 分区联络线
export function changeZoneLine(status) {
  linkCon.selectAll("path").style("opacity", `${status ? 0.3 : 1}`);
  nodeCon.selectAll("svg").style("opacity", `${status ? 0.3 : 1}`);
  nodeCon.selectAll(".Fw5TXwwEDo").style("opacity", status ? 0 : 1);
  nodeCon.selectAll("text.SDAfqbRLOiv").style("opacity", status ? 0 : 1);
  linkCon.selectAll(".topo-isDivide").style("opacity", 1);
  nodeCon.selectAll(".topo-isDivide").style("opacity", 1);

  linkCon.selectAll(".KS5tPfwEKN").style("display", status ? "none" : "block");
  nodeCon.selectAll(".s5iL6EUIpN").style("display", status ? "none" : "block");
  nodeCon.selectAll(".Zr124XA8fb").style("display", status ? "none" : "block");
  arrowCon.style("display", status ? "none" : "block");
}

//分区填充
export function changeDivideFill(status) {
  // this.topoComp.switchDivideFill(v);
  blockCon.selectAll("path").style("fill-opacity", status ? 1 : 0);
  d3.select("#svg_panel2 .mainSVG")
    .select("#block-container")
    .selectAll("path")
    .style("fill-opacity", status ? 1 : 0);
}

const sublayer35KV = "S3azOC3m2T7";
const sublayer110KV = "SeO9tbwVaU2";
export function switchTopo(value, type) {
  const commonStore = useCommonStore();
  // 如果未选择110kv 则隐藏110KV子图层的内容
  if (!value) {
    if (type === "35kV") {
      d3.select("#svg_panel2 .mainSVG").selectAll(`.${sublayer35KV}`).style("display", "none");
    }
    if (type === "110kV") {
      d3.select("#svg_panel2 .mainSVG").selectAll(`.${sublayer110KV}`).style("display", "none");
    }
  } else {
    if (type === "35kV") {
      d3.select("#svg_panel2 .mainSVG").selectAll(`.${sublayer35KV}`).style("display", "block");
    }
    if (type === "110kV") {
      d3.select("#svg_panel2 .mainSVG").selectAll(`.${sublayer110KV}`).style("display", "block");
    }
  }

  commonStore.zoneLine = false;
  commonStore.divideFill = false;

  maintainSvgPosition();

  changeZoneLine(false);
  changeDivideFill(false);
}
export function resetTopo() {
  const commonStore = useCommonStore();
  //   let defsCon = d3.select("#svg_panel .mainSVG").append("defs").attr("class", "mydefs");
  //   defsCon.html(defs);
  commonStore.zoneLine = false;
  commonStore.divideFill = false;

  resetSvgPosition();

  changeZoneLine(false);
  changeDivideFill(false);
}

function drawTooltip(list = []) {
  const commonStore = useCommonStore();

  const con = d3
    .select("#svg_panel .mainSVG")
    .select(".tooltip_group")
    .style(
      "transform",
      `translateX(${commonStore.transform.x}px) translateY(${commonStore.transform.y}px)`,
    );
  list.forEach((ele) => {
    if (ele.sublayerList && !ele.sublayerList.some((ele) => ele.sublayerId === "SERXYPcgOdl"))
      return;
    const tooltipLayout = con
      .append("foreignObject")
      .attr("class", "tooltip_content")
      .attr("data-x", ele.floatX)
      .attr("data-y", ele.floatY)
      .attr("width", 250)
      .attr("height", 65)
      .style(
        "transform",
        `translateX(${ele.floatX * commonStore.transform.k}px) translateY(${
          ele.floatY * commonStore.transform.k
        }px)`,
      )
      .on("mouseover", function (d) {
        // 将鼠标所在节点置顶
        this.parentNode.appendChild(this);
      })
      .on("wheel", function (event) {
        event.stopImmediatePropagation();
      });

    const textCon = tooltipLayout
      .append("xhtml:div")
      .style("color", "#ffffff")
      .style("font-size", "14px")
      .style("background-color", "#000000a1")
      .style("padding", "5px")
      .style("width", "100%")
      .style("height", "100%")
      .style("overflow", "auto")
      .style("user-select", "none")
      .attr("xmlns", "http://www.w3.org/1999/xhtml")
      .on("wheel", function (event) {
        event.stopImmediatePropagation();
      });

    textCon.append("xhtml:p").html(ele.repairData.stationName);
    textCon.append("xhtml:p").style("margin-top", "10px").html(ele.repairData.date);
  });

  const con2 = d3
    .select("#svg_panel2 .mainSVG")
    .select(".tooltip_group")
    .style(
      "transform",
      `translateX(${commonStore.transform.x}px) translateY(${commonStore.transform.y}px)`,
    );
  list.forEach((ele) => {
    // 如果是220全景图的数据，不渲染
    if (ele.sublayerList && ele.sublayerList.some((ele) => ele.sublayerId === "SERXYPcgOdl"))
      return;

    const tooltipLayout = con2
      .append("foreignObject")
      .attr("class", "tooltip_content")
      .attr("data-x", ele.floatX)
      .attr("data-y", ele.floatY)
      .attr("width", 250)
      .attr("height", 65)
      .style(
        "transform",
        `translateX(${ele.floatX * commonStore.transform.k}px) translateY(${
          ele.floatY * commonStore.transform.k
        }px)`,
      )
      .on("mouseover", function (d) {
        // 将鼠标所在节点置顶
        this.parentNode.appendChild(this);
      })
      .on("wheel", function (event) {
        event.stopImmediatePropagation();
      });

    const textCon = tooltipLayout
      .append("xhtml:div")
      .style("color", "#ffffff")
      .style("font-size", "14px")
      .style("background-color", "#000000a1")
      .style("padding", "5px")
      .style("width", "100%")
      .style("height", "100%")
      .style("overflow", "auto")
      .style("user-select", "none")
      .attr("xmlns", "http://www.w3.org/1999/xhtml")
      .on("wheel", function (event) {
        event.stopImmediatePropagation();
      });

    textCon.append("xhtml:p").html(ele.repairData.stationName);
    textCon.append("xhtml:p").style("margin-top", "10px").html(ele.repairData.date);
  });
}

export function setRepairTag(repairTag) {
  const topoStore = useTopoStore();

  d3.selectAll(".tooltip_group").selectAll("*").remove();
  if (repairTag) {
    const list = getRepairData(
      [...topoStore.miniSvgData.links, ...topoStore.miniSvgData.nodes],
      topoStore.repairList,
    );
    drawTooltip(list);
  }
}

export function initCon() {
  linkCon = d3.selectAll("#link-container");
  arrowCon = d3.selectAll("#arrow-container");
  nodeCon = d3.selectAll("#node-container");
  blockCon = d3.selectAll("#block-container");
  d3.select("#svg_panel .mainSVG").append("g").attr("class", "tooltip_group");
  d3.select("#svg_panel2 .mainSVG").append("g").attr("class", "tooltip_group");
}

export async function initSvg() {
  const basePath = "/dwyztApp/ftp/data/";

  if (import.meta.env.MODE === "development") {
    d3.select("#svg_panel").html(dwyzt500Raw);
    d3.select("#svg_panel2").html(dwyzt220Raw);
  } else {
    const [svg500, svg220] = await Promise.all([
      d3.xml(`${basePath}dwyzt500.svg`),
      d3.xml(`${basePath}dwyzt220.svg`),
    ]);

    d3.select("#svg_panel").node().appendChild(svg500.documentElement);
    d3.select("#svg_panel2").node().appendChild(svg220.documentElement);
  }
}

// 有功值
export function activeValue(flag) {
  // this.topoComp.activeValue(v);
  if (flag) {
    nodeCon.selectAll("text.SERXYPcgOdl").each(function () {
      if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
        d3.select(this).style("display", "block");
      }
    });

    nodeCon.selectAll("text.Fw5TXwwEDo").each(function () {
      if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
        d3.select(this).style("display", "block");
      }
    });
  } else {
    nodeCon.selectAll("text.SERXYPcgOdl").each(function () {
      if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
        d3.select(this).style("display", "none");
      }
    });

    nodeCon.selectAll("text.Fw5TXwwEDo").each(function () {
      if (d3.select(this).style("fill") == "rgb(0, 255, 0)") {
        d3.select(this).style("display", "none");
      }
    });
  }
}

export function activeStopColor(flag) {
  clearActiveStopColor();

  // 单个 橙色
  const singleFilter =
    "brightness(0) saturate(100%) invert(48%) sepia(41%) saturate(1821%) hue-rotate(9deg) brightness(107%) contrast(103%)";
  // 多个 紫色
  const multiFilter =
    "brightness(0) saturate(100%) invert(26%) sepia(67%) saturate(2653%) hue-rotate(291deg) brightness(106%) contrast(115%)";

  if (flag) {
    const topoStore = useTopoStore();

    const nodeLinkCount = new Map();

    topoStore.nodeLinkList.forEach((item) => {
      topoStore.repairList.forEach((ele) => {
        let keys = [];
        if (ele.stationIdList) {
          keys = ele.stationIdList.map((item) => getElementId(item)) || [];
        } else if (ele.stationId) {
          keys = [getElementId(ele.stationId)];
        }

        if (!item.metaData) return;
        if (keys.includes(item.metaData.rtKeyId0)) {
          const id = item.linkId
            ? `topoLink_${item.linkId.replace("#", "_")}`
            : `node_${item.nodeId.replace("#", "_")}`;
          nodeLinkCount.set(id, (nodeLinkCount.get(id) || 0) + 1);
        }
      });
    });

    console.log("🚀 ~ topoStore.repairList.forEach ~ nodeLinkCount:", nodeLinkCount);

    nodeLinkCount.forEach((count, id) => {
      const isLink = id.startsWith("topoLink_");
      const selection = isLink ? linkCon.select(`#${id}`) : nodeCon.select(`#${id}`);

      if (!selection.empty()) {
        selection
          .classed("active-stop-color", true)
          .style("filter", count >= 2 ? multiFilter : singleFilter);
      }
    });
  }
}

export function clearActiveStopColor() {
  if (linkCon) {
    linkCon.selectAll(".active-stop-color").style("filter", "none");
    linkCon.selectAll(".active-stop-color").classed("active-stop-color", false);
  }

  if (nodeCon) {
    nodeCon.selectAll(".active-stop-color").style("filter", "none");
    nodeCon.selectAll(".active-stop-color").classed("active-stop-color", false);
  }
}
