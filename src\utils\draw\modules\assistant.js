import * as d3 from "d3";
import { useCommonStore } from "@/stores/";

function getSvgSize() {
  const commonStore = useCommonStore();
  // commonStore.is110 = false;

  const containerId = !commonStore.is110 ? "svg_panel" : "svg_panel2";
  const svg = d3.select(`#${containerId} .mainSVG`);
  const scale = +svg.attr("data-scale") || 0.1;
  const mapWidth = +svg.attr("width") * scale;
  const mapHeight = +svg.attr("height") * scale;

  commonStore.svgScale = scale;

  return { mapWidth, mapHeight, scale };
}
export function initPos() {
  const { mapWidth, mapHeight } = getSvgSize();

  let width = document.getElementById("mainView").offsetWidth;
  let height = document.getElementById("mainView").offsetHeight - 10; // 减去顶部margin-top高度

  let scale = Math.min(width / mapWidth, height / mapHeight);
  let x = (width - mapWidth * scale) / 2;
  let y = (height - mapHeight * scale) / 2;

  return { x, y, k: scale };
}

// 全屏
export function fullScreen() {
  // console.log(document.fullscreenElement);
  // return
  const element = document.getElementsByClassName("mainView")[0];
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen();
  }
}
