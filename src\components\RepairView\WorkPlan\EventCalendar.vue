<template>
  <Calendar
    class="custom-calendar"
    v-model="date"
    :is-dark="isDark"
    :attributes="planStore.workPlanList"
    expanded
    @did-move="onMonthChange"
  >
    <template #day-content="{ day, attributes }">
      <div class="flex flex-col h-full z-10 overflow-hidden">
        <span class="day-label text-sm text-center bg-#1b3d59 mb-0.5">{{ day.day }}</span>
        <div class="flex flex-1 items-center justify-center">
          <div
            v-for="attr in attributes"
            :key="attr.key"
            class="text-xs leading-tight rounded-sm mt-0 mb-1"
            :class="attr.customData.class"
          >
            <p
              v-for="(data, index) in attr.customData"
              :key="data.value"
              class="cursor-pointer"
              style="display: flex; align-items: center; line-height: 1.1"
              @click="onDayclick(day, attributes, index)"
            >
              <span style="font-size: 12px">{{ data.type }}: &nbsp;</span>
              <span
                style="font-size: 14px; font-weight: bold"
                :style="{ color: attr.fontColor || '#b8c2cc' }"
                >{{ data.value }}</span
              >
            </p>
          </div>
        </div>
      </div>
    </template>
  </Calendar>
  <NSelect
    v-model:value="state"
    :options="StateOptions"
    size="small"
    class="absolute top-3 right-3 w-25"
    @update:value="getData"
  />
</template>

<script setup>
import { onBeforeUnmount, onMounted, ref } from "vue";
import { Calendar } from "v-calendar";
import "v-calendar/style.css";
import { NSelect } from "naive-ui";
import { usePlanStore } from "@/stores";
import * as d3 from "d3";
import dayjs from "dayjs";
import { StateOptions, DefaultState } from "@/utils/constant/";

const planStore = usePlanStore();

const isDark = ref(true);
const date = ref(new Date());
const dateSelected = ref({});
const activeDay = ref();
const state = ref(DefaultState);

const detailKeyList = ["start", "ondoing", "end"];
const onDayclick = (day, attributes, index) => {
  const key = detailKeyList[index];
  activeDay.value = day;
  planStore.workPlanDetails = attributes[0][key] || attributes[0]?.detail || [];
};

const getDateRange = (date) => {
  const startDate = dayjs(date).startOf("month").format("YYYY-MM-DD");
  const endDate = dayjs(date).endOf("month").format("YYYY-MM-DD");
  return { startDate, endDate };
};

const getData = () => {
  const params = {
    ...dateSelected.value,
    state: state.value,
  };
  planStore.getWorkPlanList(params);
};

const onMonthChange = (date) => {
  dateSelected.value = getDateRange(date[0].id);
  getData();
};

onMounted(() => {
  dateSelected.value = getDateRange(new Date());
  getData();
});

onBeforeUnmount(() => {
  planStore.workPlanDetails = [];
  activeDay.value = null;
});
</script>

<style lang="scss">
.vc-nav-container {
  & button {
    background-color: transparent;
  }
}
.custom-calendar {
  --day-border: 1px solid #b8c2cc;
  --day-border-highlight: 1px solid #b8c2cc;
  --day-width: 90px;
  --day-height: 70px;
  --weekday-bg: #f8fafc;
  --vc-bg: #1a2f42;
  --weekday-border: 1px solid #eaeaea;

  border-radius: 0;
  width: 100%;

  & button {
    background-color: transparent;
  }

  & .vc-title-wrapper {
    z-index: 9;
  }
  & .vc-header {
    height: 40px;
    margin-top: 0;
    background-color: var(--vc-bg);
  }
  & .vc-weeks {
    padding: 0;
  }
  & .vc-weekday {
    border-bottom: var(--weekday-border);
    border-top: var(--weekday-border);
    padding: 5px 0;
    background-color: var(--vc-bg);
  }
  & .vc-day {
    text-align: left;
    height: var(--day-height);
    color: #b8c2cc;
    background-color: var(--vc-bg);
    &:not(.on-bottom) {
      border-bottom: var(--day-border);
      &.weekday-1 {
        border-bottom: var(--day-border-highlight);
      }
    }
    &:not(.on-right) {
      border-right: var(--day-border);
    }
  }
  & .vc-day-dots {
    margin-bottom: 5px;
  }
  & .vc-pane-header-wrapper .vc-header {
    display: flex !important;
    justify-content: space-around !important;
    padding: 0 100px;
  }
  .active {
    background-color: #1b3d59;
  }
}
</style>
