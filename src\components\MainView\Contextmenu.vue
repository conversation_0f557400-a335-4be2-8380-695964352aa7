<template>
  <n-dropdown
    :show="gisStore.isContextmentVisble"
    placement="bottom-start"
    trigger="manual"
    :x="gisStore.contextmenuPos.x"
    :y="gisStore.contextmenuPos.y"
    :options="options"
    :on-clickoutside="onClickoutside"
    @select="handleSelect"
  />
  <GisModal ref="gisModalRef" />
</template>

<script setup>
import { useTemplateRef } from "vue";
import { NDropdown } from "naive-ui";
import { useGisStore } from "@/stores/";
import GisModal from "./Modal/GisModal.vue";

const options = [
  {
    label: "显示地图",
    key: "Gis",
  },
];

const gisStore = useGisStore();

const gisModalRef = useTemplateRef("gisModalRef");

const handleSelect = (key) => {
  if (key === "Gis") {
    gisModalRef.value.show();
  }
};

const onClickoutside = () => {
  gisStore.isContextmentVisble = false;
};
</script>

<style lang="scss" scoped></style>
