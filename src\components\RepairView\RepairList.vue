<template>
  <div class="overflow-y-auto overflow-x-hidden -mt-1 h-[calc(100vh-130px)]">
    <div class="border-width-1 border-b-width-0 border-#164985 text-#a1a4a9">
      <div class="flex flex-1 w-full h-10 border-b-width-1 border-#164985">
        <div class="flex w-18 justify-center items-center border-r-width-1 border-#164985">
          序号
        </div>
        <div class="flex flex-1 justify-center items-center">详情</div>
      </div>
      <div
        v-for="(item, index) in data"
        :key="index"
        class="flex border-b-width-1 border-#164985 cursor-pointer hover:opacity-85"
        @click="emit('on-row-click', item)"
      >
        <div class="w-18 flex items-center justify-center border-r-width-1 border-#164985">
          {{ item.no }}
        </div>
        <div class="flex flex-1 flex-col">
          <div class="flex border-b-width-1 border-#164985">
            <div
              class="flex items-center justify-center w-20 flex-shrink-0 border-r-width-1 border-#164985"
            >
              {{ topoStore.dataType === "检修计划" ? "检修设备" : "停电场所" }}：
            </div>
            <div class="flex items-center w-50 border-r-width-1 border-#164985 break-all p-2">
              {{ item.name }}
            </div>
            <div
              class="flex items-center justify-center w-20 flex-shrink-0 border-r-width-1 border-#164985"
            >
              {{ topoStore.dataType === "检修计划" ? "检修时间" : "时间" }}：
            </div>
            <div class="flex items-center break-all p-2">{{ item.date }}</div>
          </div>
          <div class="flex border-b-width-1 border-#164985">
            <div
              class="flex items-center justify-center w-20 flex-shrink-0 border-r-width-1 border-#164985"
            >
              停电范围：
            </div>
            <div class="p-2">{{ item.area }}</div>
          </div>
          <div class="flex">
            <div
              class="flex items-center justify-center w-20 flex-shrink-0 border-r-width-1 border-#164985"
            >
              {{ topoStore.dataType === "检修计划" ? "操作详情" : "工作内容" }}：
            </div>
            <div class="p-2">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useTopoStore } from "@/stores";
defineProps(["data"]);
const emit = defineEmits(["on-row-click"]);

const topoStore = useTopoStore();
</script>

<style></style>
