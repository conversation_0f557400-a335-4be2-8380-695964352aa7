const ColorDic = {
  3: "#fcff01", // 黄色
  4: "#ff6e00", // 橙色
  5: "#fcff01", // 黄色
};

const RepairColorDIc = {
  3: "#fcff01",
  4: "#ff6e00",
};

export const getColor = (volt, isLowbus) => {
  const colorLowbusOptions = {
    _35KV: "#4bbefc",
    _110KV: "#4bbefc",
    _220KV: "#4bbefc",
    _500KV: "#f70202",
  };

  const colorOptions = {
    _35KV: "#4bbefc",
    _110KV: "#4bbefc",
    _220KV: "#f70202",
    _500KV: "#6b83f8",
  };

  return (isLowbus ? colorLowbusOptions[volt] : colorOptions[volt]) || "#f70202";
};

export const getFillColor = (status, volt, isLowbus = false) => {
  return ColorDic[status] || getColor(volt, isLowbus);
};

export const getRepairFillColor = (status, volt, isLowbus = false) => {
  return RepairColorDIc[status] || getColor(volt, isLowbus);
};
