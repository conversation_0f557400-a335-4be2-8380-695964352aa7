import { ref } from "vue";
import { defineStore } from "pinia";
import { get } from "@/http/axios.js";
import data from "@/components/RepairView/WorkPlan/data.json";

const getColor = (num) => {
  if (num <= 5) {
    return "#20E780";
  } else if (num <= 10) {
    return "#E5D347";
  } else {
    return "#F16F1F";
  }
};
const formatData = (data) => {
  return data.map((ele) => {
    const num = ele.data.map((item) => item.value).reduce((prev, next) => prev + next, 0);
    return {
      ...ele,
      dates: new Date(ele.date),
      customData: ele.data,
      fontColor: getColor(num),
    };
  });
};
export const usePlanStore = defineStore("plan", () => {
  const workPlanDetails = ref([]);
  const workPlanList = ref([]);

  const getWorkPlanList = async (params) => {
    if (import.meta.env.MODE === "development") {
      workPlanList.value = formatData(data.data);
    } else {
      await get("/dwyzt/getScheduleByDate", params).then((res) => {
        if (res.code === "0000") {
          workPlanList.value = formatData(res.data);
        }
      });
    }
  };

  return {
    workPlanDetails,
    workPlanList,
    getWorkPlanList,
  };
});
