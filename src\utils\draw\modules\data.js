import { useTopoStore } from "@/stores/";
import { updateSvg } from "./draw";

let animationId = null;
export async function initData(mapId) {
  const topoStore = useTopoStore();
  const realData = await topoStore.getRealData(mapId);
  setTimeout(() => {
    updateSvg(realData);
  }, 2000);
}

export function freshData(mapId) {
  let lastTime = 0;
  const topoStore = useTopoStore();

  async function loop(currentTime) {
    if (currentTime - lastTime >= 60000) {
      // 间隔1分钟
      lastTime = currentTime;
      // 执行需要间隔一分钟执行的操作
      const realData = await topoStore.getRealData(mapId);
      updateSvg(realData);
    }
    animationId = requestAnimationFrame(loop);
  }
  animationId = requestAnimationFrame(loop);
}

export function cancelRefreshData() {
  cancelAnimationFrame(animationId);
}
