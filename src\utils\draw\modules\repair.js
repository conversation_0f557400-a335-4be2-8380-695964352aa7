import * as d3 from "d3";
import { getRepairFillColor } from "./constant";
import { useTopoStore } from "@/stores/";

function _updateTransStationColor(id, nodeMeta, data) {
  let con = d3.select(`#node_${id.replace("#", "_")}`);

  //变电站闪烁
  //   con.select(".xfmr").classed("topo-flash", data.rate >= 1);
  //更新主变数据
  [...con.select(".xfmr").selectAll("g").nodes()].forEach((p, i) => {
    d3.select(p)
      .selectAll(".topo_xfmr_inset")
      .style("fill", getRepairFillColor(data.xfmr[i]?.status, nodeMeta.volt));
  });

  //更新低压母线数据
  if (nodeMeta.lowBusNo !== "") {
    [...con.select(".lowbus").selectAll("g").nodes()].forEach((p, i) => {
      d3.select(p)
        .selectAll(".TransformerSubstation-inset")
        .style("fill", getRepairFillColor(data.lowBus[i]?.status, nodeMeta.volt, true));
    });
  }
  //更新高压母线数据
  if (nodeMeta.highBusNo !== "") {
    [...con.select(".highbus").selectAll("g").nodes()].forEach((p, i) => {
      d3.select(p)
        .selectAll(".TransformerSubstation-inset")
        .style("fill", getRepairFillColor(data.highBus[i]?.status, nodeMeta.volt));
    });
  }
}

function _updateLinkColor(link, data) {
  const linkCon = d3.selectAll("#link-container");

  linkCon
    .select(`#topoLink_${link.linkId.replace("#", "_")}`)
    .attr("stroke-dasharray", data.status == "6" ? "2 2" : "none")
    .style("stroke", getRepairFillColor(data.status, link.metaData.volt));
}

/**
 * 检修 (⚠️废弃)
 * @param {*} data
 */
export function updateRepairColor(data) {
  const topoStore = useTopoStore();

  topoStore.miniSvgData.nodes.forEach((node) => {
    if (node.metaData) {
      let nodeMeta = node.metaData;
      if (!data[node.nodeId]) {
        return;
      }
      switch (nodeMeta.type) {
        case "TransStation":
          _updateTransStationColor(node.nodeId, node.metaData, data[node.nodeId]);

          break;
        default:
          break;
      }
    }
  });

  topoStore.miniSvgData.links.forEach((link) => {
    if (link.metaData) {
      let nodeMeta = link.metaData;
      if (!data[link.linkId]) {
        return;
      }
      switch (nodeMeta.type) {
        case "AcLine":
          _updateLinkColor(link, data[link.linkId]);
          break;
        default:
          break;
      }
    }
  });
}
