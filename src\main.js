import "virtual:uno.css";
import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import { get, post } from "@/http/axios.js";
import { setupCalendar } from "v-calendar";

const app = createApp(App);

app.config.globalProperties.$get = get;
app.config.globalProperties.$post = post;

app.use(router).use(createPinia()).use(setupCalendar, {}).mount("#app");
