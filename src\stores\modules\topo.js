import * as d3 from "d3";
import { ref, computed, watch } from "vue";
import { defineStore } from "pinia";
import { get } from "@/http/axios.js";
import { emitter } from "@/utils/event";
import { DefaultState } from "@/utils/constant/";

export const useTopoStore = defineStore("topo", () => {
  const menuSelected = ref("");
  const isBaseVisible = ref(true);
  const repairList = ref([]);
  const balanceList = ref([]);
  let topoComp = ref(null);
  let nodeLinkList = ref([]);
  let svgData = ref(null);
  let miniSvgData = ref(null);
  let isGlobalContextmenVisible = ref(false);

  const realData = ref({});
  const colorData = ref({});

  const subList = ref([]);

  const dataType = ref("检修计划");

  const searchParams = ref({});
  const planBalanceState = ref(DefaultState);

  const setMenuSelect = (menu) => {
    menuSelected.value = menu;
    isBaseVisible.value = menu === "1";
  };

  const setTopoComp = (comp) => {
    topoComp.value = comp;
  };

  const setGlobalContextmenVisible = (data) => {
    isGlobalContextmenVisible.value = data;
  };

  const getOverHaulInfoList = async (params) => {
    try {
      let responseData;

      if (import.meta.env.MODE === "development") {
        const url = "/static/data/json/repairData.json";

        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

        const result = await response.json();
        if (result.code !== "0000") throw new Error(`API error: ${result.message}`);

        responseData = result.data;
      } else {
        const url = "/dwyzt/getBalanceInfoList";

        const result = await get(url, params);
        responseData = result.data;
      }

      // 统一处理数据结构
      repairList.value = responseData.map((ele, index) => ({
        ...ele,
        no: index + 1,
        name: ele.stationName || "--",
        date:
          ele.date || (ele.startDate && ele.endDate ? `${ele.startDate}至${ele.endDate}` : "--"),
      }));
      return repairList.value;
    } finally {
      emitter.emit("update:changeColor");
    }
  };

  /**
   * 计划平衡数据获取
   * @param {*} params
   * @param {*} isBalance
   * @returns
   */
  const getBalanceInfoListMonthly = async (params) => {
    params.state = planBalanceState.value;

    try {
      let responseData;
      if (import.meta.env.MODE === "development") {
        const url = "/static/data/json/planBalanceData_1.json";
        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

        const result = await response.json();
        if (result.code !== "0000") throw new Error(`API error: ${result.message}`);

        responseData = result.data;
      } else {
        const url = "/dwyzt/getBalanceInfoListMonthly";

        const result = await get(url, params);
        responseData = result.data;
      }

      // 统一处理数据结构
      balanceList.value = responseData.map((ele, index) => ({
        ...ele,
        no: index + 1,
        name: ele.stationName || "--",
        date:
          ele.date || (ele.startDate && ele.endDate ? `${ele.startDate}至${ele.endDate}` : "--"),
      }));
      return balanceList.value;
    } finally {
      emitter.emit("update:changeColor");
    }
  };

  /**
   *
   * @returns 获取热区列表 (⚠️废弃)
   */
  const getHotspotList = async (mapId) => {
    if (import.meta.env.MODE === "development") {
      const res = await fetch("/static/data/json/hotspotList.json");
      const res_1 = await res.json();
      if (res_1.code === "0000") {
        subList.value = res_1.data;
        return res_1.data;
      }
    } else {
      const res = await get("/dwyztApp/dwyzt/getHotspotsList", { mapId });
      if (res.code === "0000") {
        subList.value = res.data;
        return res.data;
      }
    }
  };

  /**
   *
   * @returns 获取svg所有数据
   */
  const getSvgData = async () => {
    if (svgData.value) {
      return svgData.value;
    }
    let url = "/dwyztApp/ftp/data/dwyztApp.json";
    if (import.meta.env.MODE === "development") {
      // url = "/static/data/json/666.json";
    }
    const res = await fetch(url);
    let res_1 = await res.json();
    if (import.meta.env.MODE === "development") {
      const { mapContent, mapInfo } = res_1;
      nodeLinkList.value = [...mapContent.nodes, ...mapContent.links];
      svgData.value = {
        map: mapInfo,
        nodes: mapContent.nodes,
        links: mapContent.links,
      };
      return svgData.value;
    } else {
      if (res_1.code === "0000") {
        res_1 = res_1.data;
      }
      nodeLinkList.value = [...res_1.nodes, ...res_1.links];
      svgData.value = res_1;
      return res_1;
    }
  };
  const getMiniSvgData = async () => {
    if (miniSvgData.value) {
      return miniSvgData.value;
    }
    let url = "/dwyztApp/ftp/data/dwyztApp.json";
    if (import.meta.env.MODE === "development") {
      url = "/static/data/json/dwyztmini.json";
    }
    const res = await fetch(url);
    let res_1 = await res.json();
    if (import.meta.env.MODE === "development") {
      const { mapContent, mapInfo } = res_1;
      nodeLinkList.value = [...mapContent.nodes, ...mapContent.links];
      miniSvgData.value = {
        map: mapInfo,
        nodes: mapContent.nodes,
        links: mapContent.links,
      };
      return miniSvgData.value;
    } else {
      if (res_1.code === "0000") {
        res_1 = res_1.data;
      }
      nodeLinkList.value = [...res_1.nodes, ...res_1.links];
      miniSvgData.value = res_1;
      return res_1;
    }
  };

  /**
   *
   * @returns 获取svg所有数据
   */
  const getSvgLayerData = async (mapId) => {
    let url = "/dwyztApp/dwyzt/getNodeLinkListByMapId";
    const mapInfo = await getMapInfo(mapId);

    if (!mapInfo) return;
    if (import.meta.env.MODE === "development") {
      url = "/topoEdit/getNodeLinkListByMapId";
    }

    return new Promise((resolve) => {
      get(url, { mapId: mapInfo.mapId }).then((res) => {
        if (res.code === "0000") {
          resolve({
            map: mapInfo,
            nodes: res.data.nodes,
            links: res.data.links,
          });
        }
      });
    });
  };
  /**
   * 获取mapInfo
   * @param {*} mapId
   * @returns
   */
  const getMapInfo = async (mapId) => {
    let url = "/dwyztApp/dwyzt/getMapList";
    if (import.meta.env.MODE === "development") {
      url = "/topoEdit/getMapList";
    }
    return new Promise((resolve) => {
      get(url).then((res) => {
        if (res.code === "0000") {
          const mapInfo = res.data.find((ele) => ele.mapId === mapId);
          resolve(mapInfo);
        }
      });
    });
  };

  /**
   * 获取实时数据
   * @param {*} mapId
   * @returns
   */
  const getRealData = (mapId, isSingle) => {
    return new Promise((resolve) => {
      if (import.meta.env.MODE === "development") {
        // d3.json("/static/data/json/nanjingxi.json").then(({ data }) => {
        d3.json("/static/data/json/data.json").then((data) => {
          realData.value = data;
          resolve(data);
        });
      } else {
        let url = `/dwyztApp/dwyzt/${isSingle ? "getDataByMapId" : "getRuntimeData"}`;

        get(url, { mapId: mapId }).then((res) => {
          if (res.code === "0000") {
            realData.value = res.data;
            console.log("实时数据:", res.data);
            resolve(res.data);
          }
        });
      }
    });
  };

  /**
   * 获取检修颜色数据
   * @param {*}
   * @returns
   */
  const getColorData = () => {
    return new Promise((resolve) => {
      if (import.meta.env.MODE === "development") {
        // d3.json("/static/data/json/nanjingxi.json").then(({ data }) => {
        d3.json("/static/data/json/getOverHaulStatus.json").then(({ data }) => {
          colorData.value = data;
          resolve(data);
        });
      } else {
        let url = `/dwyztApp/dwyzt/getOverHaulStatus`;

        get(url, {}).then((res) => {
          if (res.code === "0000") {
            colorData.value = res.data;
            resolve(res.data);
          }
        });
      }
    });
  };

  const isTabVisible = ref(false);
  const setTabVisible = (flag) => {
    isTabVisible.value = flag;
  };

  return {
    menuSelected,
    isBaseVisible,
    repairList,
    balanceList,
    isTabVisible,
    isGlobalContextmenVisible,
    topoComp,
    nodeLinkList,
    svgData,
    miniSvgData,
    realData,
    colorData,
    subList,
    dataType,
    searchParams,
    planBalanceState,

    setMenuSelect,
    setTopoComp,
    getOverHaulInfoList,
    getBalanceInfoListMonthly,
    getHotspotList,
    getSvgData,
    setTabVisible,
    setGlobalContextmenVisible,
    getRealData,
    getSvgLayerData,
    getMiniSvgData,
    getColorData,
  };
});
