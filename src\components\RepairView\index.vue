<template>
  <n-layout-sider
    collapse-mode="width"
    :collapsed-width="0"
    :width="620"
    show-trigger="arrow-circle"
    content-style="padding: 24px;"
    trigger-style="background-color: #6a6969;"
    collapsed-trigger-style="left: -15px; background-color: #6a6969; "
    bordered
    class="bg-#1a2f42 p-1.5"
    @update:collapsed="hideTable"
  >
    <WorkPlan v-if="topoStore.dataType === '开工计划'" />
    <RepairPlan v-else-if="topoStore.dataType === '检修计划'" />
    <PlanBalance v-else-if="topoStore.dataType === '计划平衡'" />
  </n-layout-sider>
</template>
<script setup>
import { onMounted, watch, ref, computed } from "vue";
import { useTopoStore } from "@/stores/";
import { movePosition, getElementId } from "@/utils/operateNodeLink.js";
import { moveSvgPosition } from "@/utils/draw/modules/event";
import RepairList from "@/components/RepairView/RepairList.vue";
import { NTabs, NTabPane, NLayoutSider } from "naive-ui";
import { formatOpticalCable } from "@/assets/data/OpticalCable.js";
import dayjs from "dayjs";
import WorkPlan from "@/components/RepairView/WorkPlan/index.vue";
import PlanBalance from "@/components/RepairView/PlanBalance/index.vue";
import RepairPlan from "@/components/RepairView/RepairPlan/index.vue";

const topoStore = useTopoStore();

const tabActiveName = ref("1");

// watch(
//   () => topoStore.dataType,
//   (val) => {
//     if (val === "光缆") {
//       tabActiveName.value = "4";
//     } else {
//       tabActiveName.value = "1";
//       topoStore.isTabVisible = false;
//       getTableData(tabActiveName.value);
//     }
//   },
// );

// onMounted(() => {
//   // 默认获取第一个当日检修的数据
//   getTableData("1");
// });

const hideTable = (val) => {
  moveSvgPosition(val);
};
</script>

<style>
.n-layout-sider-scroll-container {
  padding: 0 !important;
}
</style>
