<template>
  <NModal
    v-model:show="isVisible"
    class="important-repair"
    preset="card"
    display-directive="show"
    :mask-closable="false"
    style="width: 800px; height: 600px; background-color: #1a2f42"
    @after-leave="hide"
  >
    <GisView ref="gisViewRef" />
  </NModal>
</template>

<script setup>
import { ref, useTemplateRef, nextTick } from "vue";
import { NModal } from "naive-ui";
import { useGisStore } from "@/stores/";

import GisView from "../GisView/index.vue";

const gisStore = useGisStore();

const isVisible = ref(false);
const gisViewRef = useTemplateRef("gisViewRef");

function show() {
  isVisible.value = true;
  gisStore.isContextmentVisble = false;
}
function hide() {}

defineExpose({
  show,
  hide,
});
</script>
