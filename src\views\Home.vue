<template>
  <div class="home-container flex flex-col flex-1">
    <HeaderView></HeaderView>
    <router-view></router-view>
  </div>
</template>

<script setup>
import HeaderView from "@/components/HeaderView/index.vue";
</script>

<style lang="scss" scoped>
.home-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #16263d;
}

:deep(.ivu-layout-sider) {
  background: #03375f;
}
.ivu-layout-header {
  height: 80px !important;
  padding: 0 !important;
}

.ivu-tabs {
  padding-left: 10px;
}
</style>
