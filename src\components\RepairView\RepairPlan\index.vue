<template>
  <NTabs
    type="card"
    v-model:value="tabActiveName"
    @update:value="getTableData"
    @close="onTabRemove"
  >
    <template v-if="topoStore.dataType !== '光缆'">
      <NTabPane tab="当日检修" name="1">
        <RepairList :data="topoStore.repairList" @on-row-click="onRowClick"> </RepairList>
      </NTabPane>
      <NTabPane tab="当月检修" name="2">
        <RepairList :data="topoStore.repairList" @on-row-click="onRowClick"> </RepairList>
      </NTabPane>
      <NTabPane v-if="topoStore.isTabVisible" tab="查询检修" closable name="3">
        <RepairList :data="topoStore.repairList" @on-row-click="onRowClick"> </RepairList>
      </NTabPane>
    </template>
  </NTabs>
</template>

<script setup>
import dayjs from "dayjs";
import { NTabs, NTabPane, NSelect, NButton } from "naive-ui";
import { useTopoStore } from "@/stores/";
import RepairList from "../RepairList.vue";
import { onMounted, watch, ref, computed } from "vue";
import { onItemCLick } from "@/utils/operateNodeLink.js";

const topoStore = useTopoStore();
const tabActiveName = ref("1");

// 获取时间范围
const getTimeRange = (type = "1") => {
  const currentDate = dayjs();
  let startTime = "";
  let endTime = "";

  if (type === "1") {
    startTime = currentDate.format("YYYY-MM-DD");
    endTime = currentDate.format("YYYY-MM-DD");
  } else if (type === "2") {
    startTime = currentDate.startOf("month").format("YYYY-MM-DD");
    endTime = currentDate.endOf("month").format("YYYY-MM-DD");
  }

  return {
    startTime,
    endTime,
  };
};

const getTableData = async (val) => {
  // 1: 当日检修 2: 当月检修 3: 查询检修
  // 1: 次月停电 2: 第三个月
  const timeRange = val === "3" ? topoStore.searchParams : getTimeRange(val);
  console.log("🚀 ~ getTableData ~ timeRange:", timeRange);

  await topoStore.getOverHaulInfoList(timeRange);
};

onMounted(() => {
  console.log("获取检修数据");

  // 默认获取第一个当日检修的数据
  getTableData("1");
});

watch(
  () => topoStore.isTabVisible,
  (val) => {
    tabActiveName.value = val ? "3" : "1";
  },
);

const onRowClick = (row) => {
  onItemCLick(row);
};

const onTabRemove = () => {
  topoStore.setTabVisible(false);
  getTableData("1");
};
</script>

<style>
.n-tabs .n-tabs-nav.n-tabs-nav--card-type .n-tabs-tab.n-tabs-tab--active {
  background-color: #2f65a5 !important;
}
.n-tabs .n-tabs-nav.n-tabs-nav--card-type .n-tabs-tab {
  color: #b1b4b8 !important;
}
</style>
