<template>
  <div class="login">
    <div class="login-container">
      <n-form :model="loginForm" :rules="fieldRules" ref="loginForm" label-placement="left">
        <h2 class="title">系统登录</h2>
        <n-form-item label="账号" path="username">
          <n-input
            v-model:value="loginForm.username"
            placeholder="账号"
            type="text"
            @keyup.enter="login"
          >
            <template #prefix>
              <n-icon>
                <IosPersonIcon />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="密码" path="password">
          <n-input
            v-model:value="loginForm.password"
            placeholder="密码"
            type="password"
            @keyup.enter="login"
          >
            <template #prefix>
              <n-icon>
                <IosLockIcon />
              </n-icon>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item>
          <div class="flex justify-around w-full">
            <n-button @click="reset" type="primary">重 置</n-button>
            <n-button @click="login" type="primary">登 录</n-button>
          </div>
        </n-form-item>
      </n-form>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { NForm, NFormItem, NInput, NButton, NIcon, useMessage } from "naive-ui";
import IosPersonIcon from "@/assets/images/icons/IosPerson.svg";
import IosLockIcon from "@/assets/images/icons/IosLock.svg";

import { useRouter } from "vue-router";
import { post } from "@/http/axios.js";
import Cookies from "js-cookie";

const loginForm = ref({
  username: "",
  password: "",
});

const fieldRules = {
  username: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
};

const router = useRouter();

const login = () => {
  const userInfo = {
    userName: loginForm.value.username,
    password: loginForm.value.password,
  };
  let url = "/login";
  if (import.meta.env.MODE !== "development") {
    url = "/ds-action/login";
  }

  // 模拟登录请求
  post(url, userInfo).then((res) => {
    if (res.code === "0000") {
      window.$message.success("登录成功");
      // Cookies.set("token", res.data.sessionId);
      Cookies.set("token", res.data.sessionId);
      sessionStorage.setItem("token", res.data.sessionId);
      sessionStorage.setItem("displayName", res.data.displayName);
      router.replace("/index");
    }
  });
};

const reset = () => {
  loginForm.value.username = "";
  loginForm.value.password = "";
};
</script>

<style lang="scss">
.login {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("@/assets/images/login-bg.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.login-container {
  position: absolute;
  top: 25%;
  right: 15%;
  border-radius: 5px;
  background-clip: padding-box;
  margin: 100px auto;
  width: 400px;
  padding: 35px 35px 15px 35px;
  background: #333;
  border: 1px solid #444;
  box-shadow: 0 0 25px #000;
  opacity: 0.9;

  .title {
    margin: 0 auto 30px auto;
    text-align: center;
    color: #fff;
  }
}
</style>
