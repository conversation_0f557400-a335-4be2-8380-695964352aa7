<template>
  <div class="w-full h-8 flex justify-center items-center text-lg bg-#1b3d59 mt-4">详情</div>
  <NScrollbar style="max-height: 340px" class="border border-#b8c2cc border-r-0">
    <div
      v-for="(detail, index) in planStore.workPlanDetails"
      :key="index"
      class="flex cursor-pointer hover:bg-#1b3d5980"
      @click="onDetailClick(detail)"
    >
      <div class="w-20 flex justify-center items-center work-table-item">
        {{ index + 1 }}
      </div>
      <div class="flex flex-col flex-1">
        <div class="flex flex-1">
          <div class="work-table-item w-20">停电场所</div>
          <div class="work-table-item flex-1">{{ detail.stationName }}</div>
        </div>

        <div class="flex">
          <div class="work-table-item w-20">开工日期</div>
          <div class="work-table-item flex-1">{{ detail.startTime }}</div>
          <div class="work-table-item w-20">竣工日期</div>
          <div class="work-table-item flex-1">{{ detail.endTime }}</div>
        </div>
        <div class="flex">
          <div class="work-table-item w-20">停电范围</div>
          <div class="work-table-item flex-1">{{ detail.area }}</div>
        </div>
        <div class="flex">
          <div class="work-table-item w-20">工作内容</div>
          <div class="work-table-item flex-1">{{ detail.content }}</div>
        </div>
      </div>
    </div>
  </NScrollbar>
</template>

<script setup>
import { NScrollbar } from "naive-ui";
import { usePlanStore, useTopoStore } from "@/stores";
import { onItemCLick } from "@/utils/operateNodeLink.js";

const planStore = usePlanStore();
const topoStore = useTopoStore();

const getName = (detail) => {
  const { stationId, deviceName, stationName } = detail;
  if (stationId === "xl") {
    return deviceName;
  }
  return `${stationName}-${deviceName}`;
};
/**
 *
 * @param detail
 * 如果stationId是xl，名称用deviceName，定位用deviceId，代表线路
 * 如果stationId不是xl那么名称用stationName-deviceName，定位用stationId，多个的话就随机一个定位
 *
 */
const onDetailClick = (detail) => {
  onItemCLick(detail);
};
</script>

<style lang="scss">
.work-table-item {
  border-color: #b8c2cc;
  border-right: 1px solid;
  border-bottom: 1px solid;
  padding: 2px 5px;
}
</style>
