import { computed, ref } from "vue";
import { defineStore } from "pinia";

export const useCommonStore = defineStore("common", () => {
  const activeTab = ref("basic");

  const is110 = ref(false);
  const showSvg = ref(false);
  const isChange = ref(true);
  const zoneLine = ref(false);
  const divideFill = ref(false);
  const repairTag = ref(false);

  const is220ViewVisible = ref(true);
  const is110ViewVisible = ref(true);
  const is35ViewVisible = ref(true);

  const svgScale = ref(0.1);
  // fullViewList: ["SCtBKhR78xs", "SobfTgIeyvf"],
  const subLayer110List = ref(["SslirciPQil", "Sa5UltbdynS"]);
  const subLayer220List = ref(["SCtBKhR78xs"]);

  // 110kv 35KV 显示在同一个图层
  const is110KV35KVVisible = computed(() => is110ViewVisible.value || is35ViewVisible.value);

  const transform = ref({
    k: 1,
    x: 0,
    y: 0,
  });
  return {
    activeTab,
    is110,
    showSvg,
    transform,
    isChange,
    zoneLine,
    divideFill,
    repairTag,
    svgScale,

    is220ViewVisible,
    is110ViewVisible,
    is35ViewVisible,
    is110KV35KVVisible,

    subLayer110List,
    subLayer220List,
  };
});
