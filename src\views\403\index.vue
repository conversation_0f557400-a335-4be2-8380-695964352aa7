<template>
  <div class="not-found">
    <div class="warning">未授权访问</div>
    <div>
      <!-- <router-link class="go-home" to="/" replace>重新授权</router-link> -->
      <a class="go-home" @click="logout">注销</a>
    </div>
  </div>
</template>

<script>
export default {
  name: "NotFound",
  methods: {
    logout() {
      sessionStorage.clear();
      if (import.meta.env.MODE === "development") {
        // this.$post("/logout").then((res) => {
        //   if (res.code === "0000") {
        this.$Message.success("注销成功");
        this.$router.replace("/login");
        //   }
        // });
      } else {
        // window.open(import.meta.env.VITE_LOGIN_URL, "_self");
        // this.$router.replace("/");
        location.href = import.meta.env.VITE_LOGOUT_URL;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.not-found {
  text-align: center;
  padding-top: 40vh;
  background-color: #333333;
  position: fixed;
  width: 100%;
  height: 100%;
}
.go-home {
  color: #fff;
  font-size: 20px;
  font-family: sans-serif;
  font-weight: bold;
  position: relative;
  padding: 0.6em 0.4em;
  text-decoration: none;
  &:hover {
    color: #70c0e8;
  }
}
.warning {
  color: whitesmoke;
  font-size: 80px;
  font-family: sans-serif;
  font-weight: bold;
  position: relative;
}
</style>
