html,
body,
#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
#app {
  font-family: "Microsoft YaHei", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // text-align: center;
  .ivu-page {
    text-align: center;
  }
}
#nav {
  padding: 30px;
  a {
    font-weight: bold;
    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: insert 0 0 5px rgba(0, 0, 0, 0.2);
  background: #b3b3b3;
}
::-webkit-scrollbar-track {
  border-radius: 10px;
  -webkit-box-shadow: insert 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
}
::-webkit-scrollbar-thumb:hover {
  background: #b6b4b4;
}

.power-grid-layout {
  width: 100%;
  height: calc(100% - 60px);
  padding: 0 30px 15px 30px;
  background-image: url(./assets/images/power-grid-bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.power-grid-title {
  position: relative;
  font-size: 20px;
  padding-left: 10px;
  font-weight: bold;
  background: linear-gradient(to bottom, #d9d9d9, #a6a6a6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: silver;
}
.power-grid-divide {
  position: absolute;
  bottom: 9px;
  left: 0;
  width: 100%;
  border-bottom: 2px solid #e6e6e61e;
  &::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 250px;
    height: 2px;
    background: #48649bcb;
  }
}
.power-grid-wrapper {
  height: calc(100% - 48px);
}

.power-grid-card {
  background-color: rgba($color: #174a91, $alpha: 0.3);
  border-radius: 4px;
  position: relative;
}

.card-middle-value-content {
  position: relative;
  background-color: rgba($color: #0b2345, $alpha: 0.35);
}
.card-middle-value {
  font-size: 22px;
  font-weight: bold;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background-image: linear-gradient(to bottom, #00c7f4 0%, #77f3f6 80%, #ffffff 100%);
}


.topo-flash {
    animation: flash 0.8s linear infinite;
}

.topo-flash-node {
    filter: sepia(1);
    animation: flash 0.8s linear infinite;
}

@keyframes flash {
    from {
        opacity: 1;
    }

    to {
        opacity: 0.25;
    }
}