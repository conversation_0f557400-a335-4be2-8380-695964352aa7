import { ref } from "vue";
import debounce from "lodash/debounce";
import { getSearchListByPinyin } from "@/utils/searchChinese.js";
import { useTopoStore } from "@/stores/";

export const useNodeLinkSearch = (minusHeight) => {
  const nodeLinkOptions = ref([]);
  const topoStore = useTopoStore();

  // 远程搜索
  const remoteMethod = debounce((query) => {
    if (query !== "") {
      const linkList = topoStore.miniSvgData.links
        .filter((i) => {
          return i.metaData && i.metaData.rtKeyIdDesc;
        })
        .map((item) => {
          return {
            value: item.linkId,
            label: item.metaData.rtKeyIdDesc,
            linkId: item.linkId,
            type: "link",
            linkPath: item.linkPath,
          };
        });

      const nodeList = topoStore.miniSvgData.nodes
        .filter((i) => {
          return i.metaData && i.metaData.name && i.nodeType != "text";
        })
        .map((item) => {
          return {
            value: item.nodeId,
            label: item.metaData.name,
            nodeId: item.nodeId,
            type: "node",
            nodePosition: item.nodePosition,
            // mapId:item.mapId
          };
        });

      const list = linkList
        .filter((i) => {
          return i.label && i.label != "";
        })
        .concat(
          nodeList.filter((i) => {
            return i.label && i.label != "";
          }),
        );
      nodeLinkOptions.value = getSearchListByPinyin(list, query, "label");
    } else {
      nodeLinkOptions.value = [];
    }
  }, 300);

  return {
    nodeLinkOptions,
    remoteMethod,
  };
};
