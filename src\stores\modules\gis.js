import { ref } from "vue";
import { defineStore } from "pinia";

export const useGisStore = defineStore("gis", () => {
  const isContextmentVisble = ref(false);
  const deviceId = ref("");
  const contextmenuPos = ref({
    x: 0,
    y: 0,
  });

  const mapUrl = ref("");
  const mapData = ref([]);

  return {
    isContextmentVisble,
    deviceId,
    contextmenuPos,
    mapUrl,
    mapData,
  };
});
